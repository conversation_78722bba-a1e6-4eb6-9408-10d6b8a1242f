# Volcano Agent

A Node.js application using the Volcano SDK to run AI agents with OpenAI. This application can search for MCP servers and interact with them to get <PERSON> jokes or find GitHub issues servers.

## Features

- 🔍 **MCP Server Discovery**: Search for MCP servers in a registry
- 🎭 **<PERSON>**: Get jokes from <PERSON> MCP servers
- 🐙 **GitHub Integration**: Find MCP servers for GitHub issues
- 📝 **Structured Logging**: Comprehensive logging with timestamps and emojis
- 🛠️ **CLI Interface**: Command-line options for different use cases
- 🔧 **Debug Mode**: Detailed debugging information
- 📦 **Modular Architecture**: Well-organized code structure

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set your OpenAI API key:
   ```bash
   export OPENAI_API_KEY="your-api-key-here"
   ```

## Usage

### Basic Usage
```bash
# Get a <PERSON> joke about history (default)
npm run dev

# Get a <PERSON> joke about programming
npm run dev -- --topic programming

# Search for GitHub issues MCP servers
npm run dev -- --search-type github-issues

# Enable debug logging
npm run dev -- --debug
```

### Command Line Options

- `-s, --search-type <type>`: Search type: 'chuck-norris' or 'github-issues' (default: chuck-norris)
- `-t, --topic <topic>`: Topic for Chuck Norris jokes (default: history)
- `-d, --debug`: Enable debug logging
- `-h, --help`: Show help message

### Examples

```bash
# Different joke topics
npm run dev -- -t "space exploration"
npm run dev -- -t "artificial intelligence"

# GitHub issues search with debug
npm run dev -- -s github-issues --debug

# Help
npm run dev -- --help
```

### Production

```bash
# Build the TypeScript code
npm run build

# Run the compiled JavaScript
npm start
```

## Architecture

The application is organized into several modules:

- **`agent.ts`** - Main application logic and orchestration
- **`config.ts`** - Configuration constants and type definitions
- **`logger.ts`** - Structured logging utility
- **`cli.ts`** - Command-line interface parsing

### Key Features

1. **Type Safety**: Full TypeScript support with proper interfaces
2. **Error Handling**: Comprehensive error handling with graceful failures
3. **Environment Validation**: Validates required environment variables
4. **Structured Responses**: Uses JSON Schema for reliable API responses
5. **Modular Design**: Separated concerns for maintainability

## Configuration

The application supports the following environment variables:

- `OPENAI_API_KEY` (required): Your OpenAI API key
- `DEBUG` (optional): Set to 'true' to enable debug logging

## Available Scripts

- `npm run dev` - Run the TypeScript source directly using tsx
- `npm run build` - Compile TypeScript to JavaScript in the `dist/` folder
- `npm start` - Run the compiled JavaScript from `dist/agent.js`
- `npm run clean` - Remove the `dist/` folder

## Technical Details

- **ES Modules**: Configured as ES module with top-level await support
- **TypeScript**: ES2022 target with proper module resolution
- **JSON Schema**: Structured outputs with validation
- **Error Recovery**: Graceful handling of API failures
