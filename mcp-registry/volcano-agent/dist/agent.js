import { agent, llmOpenAI, llmOpenAIResponses, mcp } from "volcano-sdk";
import { CONFIG } from './config.js';
import { Logger } from './logger.js';
import { parseArgs, getSearchQuery } from './cli.js';
// Validate environment variables
function validateEnvironment() {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
        Logger.error('OPENAI_API_KEY environment variable is required');
        process.exit(1);
    }
    Logger.success('Environment validation passed');
    return apiKey;
}
// Create LLM instances
function createLLMInstances(apiKey) {
    const llm = llmOpenAI({
        apiKey,
        model: CONFIG.OPENAI_MODEL,
    });
    const structuredLlm = llmOpenAIResponses({
        apiKey,
        model: CONFIG.OPENAI_MODEL,
        options: {
            jsonSchema: {
                name: "mcp_response",
                description: "MCP Server information",
                schema: {
                    type: "object",
                    properties: {
                        data: {
                            type: "array",
                            items: {
                                type: "object",
                                properties: {
                                    url: { type: "string" },
                                    name: { type: "string" },
                                    description: { type: "string" },
                                },
                                required: ["url", "name", "description"],
                                additionalProperties: false,
                            },
                        },
                    },
                    required: ["data"],
                    additionalProperties: false,
                },
            },
        }
    });
    return { llm, structuredLlm };
}
// Search for MCP servers
async function searchMCPServers(structuredLlm, query) {
    Logger.info(`Searching for MCP servers: ${query}`, '🔍');
    const registryMcp = mcp(CONFIG.REGISTRY_URL);
    try {
        const results = await agent({ llm: structuredLlm })
            .then({
            prompt: query,
            mcps: [registryMcp],
        })
            .run();
        // Debug: Log tool calls for the first result
        if (results[0]?.toolCalls) {
            Logger.debug('Tool calls from first result', results[0].toolCalls, '🔧');
        }
        const lastResult = results[results.length - 1];
        if (!lastResult?.llmOutput) {
            throw new Error('No LLM output received from search');
        }
        const response = JSON.parse(lastResult.llmOutput || CONFIG.DEFAULT_RESPONSE);
        return response.data || [];
    }
    catch (error) {
        Logger.error(`Error searching MCP servers: ${error}`);
        return [];
    }
}
// Get a joke from Chuck Norris MCP server
async function getChuckNorrisJoke(llm, serverUrl, topic) {
    Logger.info(`Getting Chuck Norris joke about: ${topic}`, '😄');
    try {
        const chuckNorrisMcp = mcp(serverUrl);
        const results = await agent({ llm })
            .then({
            prompt: `Tell me a Chuck Norris joke about ${topic}`,
            mcps: [chuckNorrisMcp],
        })
            .run();
        const lastResult = results[results.length - 1];
        return lastResult?.llmOutput || 'No joke received';
    }
    catch (error) {
        Logger.error(`Error getting Chuck Norris joke: ${error}`);
        return 'Failed to get joke';
    }
}
// Main execution function
async function main() {
    try {
        // Parse command line arguments
        const options = parseArgs();
        Logger.info(`Starting Volcano Agent for ${options.searchType}`, '🌋');
        Logger.debug('CLI options', options);
        // Validate environment and create LLM instances
        const apiKey = validateEnvironment();
        const { llm, structuredLlm } = createLLMInstances(apiKey);
        // Search for MCP servers based on search type
        const searchQuery = getSearchQuery(options.searchType);
        const mcpServers = await searchMCPServers(structuredLlm, searchQuery);
        Logger.info(`Found ${mcpServers.length} MCP servers`, '📋');
        Logger.debug('MCP servers details', mcpServers);
        if (mcpServers.length === 0) {
            Logger.error(`No MCP servers found for ${options.searchType}`);
            process.exit(1);
        }
        // Use the first server
        const selectedServer = mcpServers[0];
        Logger.success(`Using MCP server: ${selectedServer.name} (${selectedServer.url})`);
        // Handle different search types
        if (options.searchType === 'chuck-norris') {
            // Get a Chuck Norris joke
            const joke = await getChuckNorrisJoke(llm, selectedServer.url, options.topic);
            Logger.joke('\nChuck Norris Joke:');
            console.log(joke);
        }
        else if (options.searchType === 'github-issues') {
            Logger.info('GitHub issues MCP server found. You can now use it to check your issues.');
            Logger.info(`Server URL: ${selectedServer.url}`);
            Logger.info(`Description: ${selectedServer.description}`);
        }
        Logger.success('Application completed successfully', '🎉');
    }
    catch (error) {
        Logger.error(`Fatal error: ${error}`, '💥');
        process.exit(1);
    }
}
// Run the application
main().catch((error) => {
    Logger.error(`Unhandled error: ${error}`, '🚨');
    process.exit(1);
});
