import { CONFIG } from './config.js';
export function parseArgs() {
    const args = process.argv.slice(2);
    const options = {
        searchType: 'chuck-norris',
        topic: 'history',
        debug: false,
    };
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        switch (arg) {
            case '--search-type':
            case '-s':
                const searchType = args[++i];
                if (searchType === 'chuck-norris' || searchType === 'github-issues') {
                    options.searchType = searchType;
                }
                else {
                    console.error(`Invalid search type: ${searchType}. Use 'chuck-norris' or 'github-issues'`);
                    process.exit(1);
                }
                break;
            case '--topic':
            case '-t':
                options.topic = args[++i] || 'history';
                break;
            case '--debug':
            case '-d':
                options.debug = true;
                process.env.DEBUG = 'true';
                break;
            case '--help':
            case '-h':
                printHelp();
                process.exit(0);
                break;
            default:
                console.error(`Unknown argument: ${arg}`);
                printHelp();
                process.exit(1);
        }
    }
    return options;
}
function printHelp() {
    console.log(`
Volcano Agent - MCP Server Client

Usage: npm run dev [options]

Options:
  -s, --search-type <type>    Search type: 'chuck-norris' or 'github-issues' (default: chuck-norris)
  -t, --topic <topic>         Topic for Chuck Norris jokes (default: history)
  -d, --debug                 Enable debug logging
  -h, --help                  Show this help message

Examples:
  npm run dev                                    # Get a Chuck Norris joke about history
  npm run dev -s chuck-norris -t programming     # Get a Chuck Norris joke about programming
  npm run dev -s github-issues                  # Search for GitHub issues MCP servers
  npm run dev --debug                           # Enable debug logging

Environment Variables:
  OPENAI_API_KEY    Required: Your OpenAI API key
  DEBUG             Optional: Set to 'true' to enable debug logging
`);
}
export function getSearchQuery(searchType) {
    switch (searchType) {
        case 'chuck-norris':
            return CONFIG.PROMPTS.CHUCK_NORRIS_SEARCH;
        case 'github-issues':
            return CONFIG.PROMPTS.GITHUB_ISSUES_SEARCH;
        default:
            return CONFIG.PROMPTS.CHUCK_NORRIS_SEARCH;
    }
}
