export declare const CONFIG: {
    readonly OPENAI_MODEL: "gpt-4o-mini";
    readonly REGISTRY_URL: "http://localhost:8000/mcp/registry";
    readonly DEFAULT_RESPONSE: "{\"data\":[]}";
    readonly PROMPTS: {
        readonly CHUCK_NORRIS_SEARCH: "Search for a MCP server that handles chuck norris jokes. Include url, name, and description for each server.";
        readonly GITHUB_ISSUES_SEARCH: "Return all MCP servers that allow me to check my github issues. Include url, name, and description for each server.";
    };
    readonly ENABLE_DEBUG: boolean;
};
export interface MCPServer {
    url: string;
    name: string;
    description: string;
}
export interface MCPResponse {
    data: MCPServer[];
}
