export declare class Logger {
    private static formatMessage;
    static info(message: string, emoji?: string): void;
    static success(message: string, emoji?: string): void;
    static warning(message: string, emoji?: string): void;
    static error(message: string, emoji?: string): void;
    static debug(message: string, data?: any, emoji?: string): void;
    static joke(message: string, emoji?: string): void;
}
