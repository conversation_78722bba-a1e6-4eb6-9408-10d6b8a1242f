import { CONFIG } from './config.js';
export class Logger {
    static formatMessage(level, message, emoji = '') {
        const timestamp = new Date().toISOString();
        return `[${timestamp}] ${emoji} ${level.toUpperCase()}: ${message}`;
    }
    static info(message, emoji = 'ℹ️') {
        console.log(this.formatMessage('info', message, emoji));
    }
    static success(message, emoji = '✅') {
        console.log(this.formatMessage('success', message, emoji));
    }
    static warning(message, emoji = '⚠️') {
        console.warn(this.formatMessage('warning', message, emoji));
    }
    static error(message, emoji = '❌') {
        console.error(this.formatMessage('error', message, emoji));
    }
    static debug(message, data, emoji = '🔍') {
        if (CONFIG.ENABLE_DEBUG) {
            console.log(this.formatMessage('debug', message, emoji));
            if (data !== undefined) {
                console.log(JSON.stringify(data, null, 2));
            }
        }
    }
    static joke(message, emoji = '🎭') {
        console.log(this.formatMessage('joke', message, emoji));
    }
}
