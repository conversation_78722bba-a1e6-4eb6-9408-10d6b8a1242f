{"version": 3, "file": "sse.js", "sourceRoot": "", "sources": ["../../../src/client/sse.ts"], "names": [], "mappings": ";;;AAAA,6CAAiF;AACjF,yDAAqG;AACrG,0CAAmE;AACnE,uCAAmH;AAEnH,MAAa,QAAS,SAAQ,KAAK;IAC/B,YACoB,IAAwB,EACxC,OAA2B,EACX,KAAiB;QAEjC,KAAK,CAAC,cAAc,OAAO,EAAE,CAAC,CAAC;QAJf,SAAI,GAAJ,IAAI,CAAoB;QAExB,UAAK,GAAL,KAAK,CAAY;IAGrC,CAAC;CACJ;AARD,4BAQC;AA2CD;;;;GAIG;AACH,MAAa,kBAAkB;IAkB3B,YAAY,GAAQ,EAAE,IAAgC;QAClD,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,EAAE,eAAe,CAAC;QAC9C,IAAI,CAAC,YAAY,GAAG,IAAI,EAAE,WAAW,CAAC;QACtC,IAAI,CAAC,aAAa,GAAG,IAAI,EAAE,YAAY,CAAC;QACxC,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,KAAK,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,IAAA,kCAAmB,EAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;IAC9E,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,MAAM,IAAI,2BAAiB,CAAC,kBAAkB,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,MAAkB,CAAC;QACvB,IAAI,CAAC;YACD,MAAM,GAAG,MAAM,IAAA,cAAI,EAAC,IAAI,CAAC,aAAa,EAAE;gBACpC,SAAS,EAAE,IAAI,CAAC,IAAI;gBACpB,mBAAmB,EAAE,IAAI,CAAC,oBAAoB;gBAC9C,KAAK,EAAE,IAAI,CAAC,MAAM;gBAClB,OAAO,EAAE,IAAI,CAAC,cAAc;aAC/B,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,OAAO,EAAE,CAAC,KAAc,CAAC,CAAC;YAC/B,MAAM,KAAK,CAAC;QAChB,CAAC;QAED,IAAI,MAAM,KAAK,YAAY,EAAE,CAAC;YAC1B,MAAM,IAAI,2BAAiB,EAAE,CAAC;QAClC,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,MAAM,OAAO,GAAyC,EAAE,CAAC;QACzD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,MAAM,EAAE,CAAC;gBACT,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,MAAM,CAAC,YAAY,EAAE,CAAC;YAC/D,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,OAAO,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC5D,CAAC;QAED,MAAM,YAAY,GAAG,IAAA,+BAAgB,EAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAElE,OAAO,IAAI,OAAO,CAAC;YACf,GAAG,OAAO;YACV,GAAG,YAAY;SAClB,CAAC,CAAC;IACP,CAAC;IAEO,YAAY;QAChB,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,gBAAgB,EAAE,KAAK,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,CAAiB,CAAC;QAC1F,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,YAAY,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBAChD,GAAG,IAAI,CAAC,gBAAgB;gBACxB,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;oBACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;oBAC5C,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC;oBAC3C,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,GAAG,EAAE;wBAClC,GAAG,IAAI;wBACP,OAAO;qBACV,CAAC,CAAC;oBAEH,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC;wBACtE,MAAM,EAAE,mBAAmB,EAAE,KAAK,EAAE,GAAG,IAAA,sCAA4B,EAAC,QAAQ,CAAC,CAAC;wBAC9E,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;wBAChD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;oBACxB,CAAC;oBAED,OAAO,QAAQ,CAAC;gBACpB,CAAC;aACJ,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,EAAE,CAAC;YAE9C,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE;gBAChC,IAAI,KAAK,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBAC3C,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;oBAC5C,OAAO;gBACX,CAAC;gBAED,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC7D,MAAM,CAAC,KAAK,CAAC,CAAC;gBACd,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,EAAE;gBAC5B,+EAA+E;YACnF,CAAC,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,KAAY,EAAE,EAAE;gBAC5D,MAAM,YAAY,GAAG,KAAqB,CAAC;gBAE3C,IAAI,CAAC;oBACD,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;oBACvD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;wBAC7C,MAAM,IAAI,KAAK,CAAC,qDAAqD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;oBAClG,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,MAAM,CAAC,KAAK,CAAC,CAAC;oBACd,IAAI,CAAC,OAAO,EAAE,CAAC,KAAc,CAAC,CAAC;oBAE/B,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;oBAClB,OAAO;gBACX,CAAC;gBAED,OAAO,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,CAAC,KAAY,EAAE,EAAE;gBAC3C,MAAM,YAAY,GAAG,KAAqB,CAAC;gBAC3C,IAAI,OAAuB,CAAC;gBAC5B,IAAI,CAAC;oBACD,OAAO,GAAG,+BAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;gBACxE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,IAAI,CAAC,OAAO,EAAE,CAAC,KAAc,CAAC,CAAC;oBAC/B,OAAO;gBACX,CAAC;gBAED,IAAI,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC;YAC9B,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,KAAK;QACP,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,6GAA6G,CAAC,CAAC;QACnI,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,iBAAyB;QACtC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,MAAM,IAAI,2BAAiB,CAAC,kBAAkB,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAA,cAAI,EAAC,IAAI,CAAC,aAAa,EAAE;YAC1C,SAAS,EAAE,IAAI,CAAC,IAAI;YACpB,iBAAiB;YACjB,mBAAmB,EAAE,IAAI,CAAC,oBAAoB;YAC9C,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,OAAO,EAAE,IAAI,CAAC,cAAc;SAC/B,CAAC,CAAC;QACH,IAAI,MAAM,KAAK,YAAY,EAAE,CAAC;YAC1B,MAAM,IAAI,2BAAiB,CAAC,qBAAqB,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED,KAAK,CAAC,KAAK;QACP,IAAI,CAAC,gBAAgB,EAAE,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAuB;QAC9B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;YAChD,MAAM,IAAI,GAAG;gBACT,GAAG,IAAI,CAAC,YAAY;gBACpB,MAAM,EAAE,MAAM;gBACd,OAAO;gBACP,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC7B,MAAM,EAAE,IAAI,CAAC,gBAAgB,EAAE,MAAM;aACxC,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YACpE,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACf,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;gBAErD,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBAChD,MAAM,EAAE,mBAAmB,EAAE,KAAK,EAAE,GAAG,IAAA,sCAA4B,EAAC,QAAQ,CAAC,CAAC;oBAC9E,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;oBAChD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;oBAEpB,MAAM,MAAM,GAAG,MAAM,IAAA,cAAI,EAAC,IAAI,CAAC,aAAa,EAAE;wBAC1C,SAAS,EAAE,IAAI,CAAC,IAAI;wBACpB,mBAAmB,EAAE,IAAI,CAAC,oBAAoB;wBAC9C,KAAK,EAAE,IAAI,CAAC,MAAM;wBAClB,OAAO,EAAE,IAAI,CAAC,cAAc;qBAC/B,CAAC,CAAC;oBACH,IAAI,MAAM,KAAK,YAAY,EAAE,CAAC;wBAC1B,MAAM,IAAI,2BAAiB,EAAE,CAAC;oBAClC,CAAC;oBAED,0DAA0D;oBAC1D,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC9B,CAAC;gBAED,MAAM,IAAI,KAAK,CAAC,mCAAmC,QAAQ,CAAC,MAAM,MAAM,IAAI,EAAE,CAAC,CAAC;YACpF,CAAC;YAED,iEAAiE;YACjE,MAAM,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,OAAO,EAAE,CAAC,KAAc,CAAC,CAAC;YAC/B,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,kBAAkB,CAAC,OAAe;QAC9B,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;IACpC,CAAC;CACJ;AA1OD,gDA0OC"}