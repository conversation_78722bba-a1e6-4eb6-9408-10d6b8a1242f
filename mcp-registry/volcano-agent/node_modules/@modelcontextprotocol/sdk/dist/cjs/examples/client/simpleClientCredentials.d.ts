#!/usr/bin/env node
/**
 * Example demonstrating client_credentials grant for machine-to-machine authentication.
 *
 * Supports two authentication methods based on environment variables:
 *
 * 1. client_secret_basic (default):
 *    MCP_CLIENT_ID - OAuth client ID (required)
 *    MCP_CLIENT_SECRET - OAuth client secret (required)
 *
 * 2. private_key_jwt (when MCP_CLIENT_PRIVATE_KEY_PEM is set):
 *    MCP_CLIENT_ID - OAuth client ID (required)
 *    MCP_CLIENT_PRIVATE_KEY_PEM - PEM-encoded private key for JWT signing (required)
 *    MCP_CLIENT_ALGORITHM - Signing algorithm (default: RS256)
 *
 * Common:
 *    MCP_SERVER_URL - Server URL (default: http://localhost:3000/mcp)
 */
export {};
//# sourceMappingURL=simpleClientCredentials.d.ts.map