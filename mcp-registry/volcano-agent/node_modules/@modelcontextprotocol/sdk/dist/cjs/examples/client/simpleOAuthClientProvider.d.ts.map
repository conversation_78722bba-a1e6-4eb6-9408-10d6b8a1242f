{"version": 3, "file": "simpleOAuthClientProvider.d.ts", "sourceRoot": "", "sources": ["../../../../src/examples/client/simpleOAuthClientProvider.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,sBAAsB,CAAC;AAC3D,OAAO,EAAE,2BAA2B,EAAE,mBAAmB,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AAErG;;;GAGG;AACH,qBAAa,2BAA4B,YAAW,mBAAmB;IAM/D,OAAO,CAAC,QAAQ,CAAC,YAAY;IAC7B,OAAO,CAAC,QAAQ,CAAC,eAAe;aAEhB,iBAAiB,CAAC,EAAE,MAAM;IAR9C,OAAO,CAAC,kBAAkB,CAAC,CAA8B;IACzD,OAAO,CAAC,OAAO,CAAC,CAAc;IAC9B,OAAO,CAAC,aAAa,CAAC,CAAS;gBAGV,YAAY,EAAE,MAAM,GAAG,GAAG,EAC1B,eAAe,EAAE,mBAAmB,EACrD,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,EACf,iBAAiB,CAAC,EAAE,MAAM,YAAA;IAS9C,OAAO,CAAC,WAAW,CAAqB;IAExC,IAAI,WAAW,IAAI,MAAM,GAAG,GAAG,CAE9B;IAED,IAAI,cAAc,IAAI,mBAAmB,CAExC;IAED,iBAAiB,IAAI,2BAA2B,GAAG,SAAS;IAI5D,qBAAqB,CAAC,iBAAiB,EAAE,2BAA2B,GAAG,IAAI;IAI3E,MAAM,IAAI,WAAW,GAAG,SAAS;IAIjC,UAAU,CAAC,MAAM,EAAE,WAAW,GAAG,IAAI;IAIrC,uBAAuB,CAAC,gBAAgB,EAAE,GAAG,GAAG,IAAI;IAIpD,gBAAgB,CAAC,YAAY,EAAE,MAAM,GAAG,IAAI;IAI5C,YAAY,IAAI,MAAM;CAMzB"}