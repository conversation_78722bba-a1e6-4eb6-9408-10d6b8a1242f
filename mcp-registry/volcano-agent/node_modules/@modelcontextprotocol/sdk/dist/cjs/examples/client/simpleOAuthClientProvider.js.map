{"version": 3, "file": "simpleOAuthClientProvider.js", "sourceRoot": "", "sources": ["../../../../src/examples/client/simpleOAuthClientProvider.ts"], "names": [], "mappings": ";;;AAGA;;;GAGG;AACH,MAAa,2BAA2B;IAKpC,YACqB,YAA0B,EAC1B,eAAoC,EACrD,UAA+B,EACf,iBAA0B;QAHzB,iBAAY,GAAZ,YAAY,CAAc;QAC1B,oBAAe,GAAf,eAAe,CAAqB;QAErC,sBAAiB,GAAjB,iBAAiB,CAAS;QAE1C,IAAI,CAAC,WAAW;YACZ,UAAU;gBACV,CAAC,GAAG,CAAC,EAAE;oBACH,OAAO,CAAC,GAAG,CAAC,gBAAgB,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC;IACX,CAAC;IAID,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAI,cAAc;QACd,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,iBAAiB;QACb,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED,qBAAqB,CAAC,iBAA8C;QAChE,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;IAChD,CAAC;IAED,MAAM;QACF,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,UAAU,CAAC,MAAmB;QAC1B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,uBAAuB,CAAC,gBAAqB;QACzC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;IACvC,CAAC;IAED,gBAAgB,CAAC,YAAoB;QACjC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IACtC,CAAC;IAED,YAAY;QACR,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;CACJ;AA1DD,kEA0DC"}