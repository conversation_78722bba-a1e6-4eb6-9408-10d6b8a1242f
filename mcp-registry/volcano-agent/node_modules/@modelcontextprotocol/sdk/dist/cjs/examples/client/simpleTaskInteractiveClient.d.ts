/**
 * Simple interactive task client demonstrating elicitation and sampling responses.
 *
 * This client connects to simpleTaskInteractive.ts server and demonstrates:
 * - Handling elicitation requests (y/n confirmation)
 * - Handling sampling requests (returns a hardcoded haiku)
 * - Using task-based tool execution with streaming
 */
export {};
//# sourceMappingURL=simpleTaskInteractiveClient.d.ts.map