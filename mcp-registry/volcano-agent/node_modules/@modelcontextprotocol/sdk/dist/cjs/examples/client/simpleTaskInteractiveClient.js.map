{"version": 3, "file": "simpleTaskInteractiveClient.js", "sourceRoot": "", "sources": ["../../../../src/examples/client/simpleTaskInteractiveClient.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;AAEH,oDAA+C;AAC/C,sEAA+E;AAC/E,iDAAgD;AAChD,6CASwB;AAExB,2CAA2C;AAC3C,MAAM,QAAQ,GAAG,IAAA,+BAAe,EAAC;IAC7B,KAAK,EAAE,OAAO,CAAC,KAAK;IACpB,MAAM,EAAE,OAAO,CAAC,MAAM;CACzB,CAAC,CAAC;AAEH,SAAS,QAAQ,CAAC,MAAc;IAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QACzB,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;YAC/B,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,cAAc,CAAC,MAA2D;IAC/E,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAoB,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;IACpF,OAAO,WAAW,EAAE,IAAI,IAAI,WAAW,CAAC;AAC5C,CAAC;AAED,KAAK,UAAU,mBAAmB,CAAC,MAIlC;IACG,OAAO,CAAC,GAAG,CAAC,gCAAgC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;IAE9D,iCAAiC;IACjC,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,uBAAuB,CAAC,CAAC;IACzD,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;IAE7E,OAAO,CAAC,GAAG,CAAC,0CAA0C,SAAS,EAAE,CAAC,CAAC;IACnE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,CAAC;AACjE,CAAC;AAED,KAAK,UAAU,gBAAgB,CAAC,MAAsC;IAClE,wCAAwC;IACxC,IAAI,MAAM,GAAG,SAAS,CAAC;IACvB,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAChD,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;QACrC,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,IAAI,OAAO,EAAE,CAAC;YACzG,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1B,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC;YACrE,IAAI,QAAQ,IAAI,MAAM,IAAI,QAAQ,EAAE,CAAC;gBACjC,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC3B,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,oDAAoD,MAAM,EAAE,CAAC,CAAC;IAE1E,6DAA6D;IAC7D,MAAM,KAAK,GAAG;;wBAEM,CAAC;IAErB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAChD,OAAO;QACH,KAAK,EAAE,kBAAkB;QACzB,IAAI,EAAE,WAAW;QACjB,OAAO,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE;KACzC,CAAC;AACN,CAAC;AAED,KAAK,UAAU,GAAG,CAAC,GAAW;IAC1B,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,iBAAiB,GAAG,KAAK,CAAC,CAAC;IAEvC,2DAA2D;IAC3D,MAAM,MAAM,GAAG,IAAI,iBAAM,CACrB,EAAE,IAAI,EAAE,gCAAgC,EAAE,OAAO,EAAE,OAAO,EAAE,EAC5D;QACI,YAAY,EAAE;YACV,WAAW,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;YACzB,QAAQ,EAAE,EAAE;SACf;KACJ,CACJ,CAAC;IAEF,qCAAqC;IACrC,MAAM,CAAC,iBAAiB,CAAC,8BAAmB,EAAE,KAAK,EAAC,OAAO,EAAC,EAAE;QAC1D,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxD,MAAM,IAAI,mBAAQ,CAAC,oBAAS,CAAC,aAAa,EAAE,iCAAiC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QACxG,CAAC;QACD,OAAO,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC;IAEH,kCAAkC;IAClC,MAAM,CAAC,iBAAiB,CAAC,qCAA0B,EAAE,KAAK,EAAC,OAAO,EAAC,EAAE;QACjE,OAAO,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAmD,CAAC;IAC9F,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,MAAM,SAAS,GAAG,IAAI,iDAA6B,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAClE,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAChC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAE5B,aAAa;IACb,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,oBAAoB,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEjF,uCAAuC;IACvC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAE9C,MAAM,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,CAC1D,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE,EAAE,EACpE,+BAAoB,EACpB,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,CAC3B,CAAC;IAEF,IAAI,KAAK,EAAE,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;QACxC,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,aAAa;gBACd,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;gBACpD,MAAM;YACV,KAAK,YAAY;gBACb,OAAO,CAAC,GAAG,CAAC,gBAAgB,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;gBACnD,MAAM;YACV,KAAK,QAAQ;gBACT,OAAO,CAAC,GAAG,CAAC,WAAW,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACzD,MAAM;YACV,KAAK,OAAO;gBACR,OAAO,CAAC,KAAK,CAAC,UAAU,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;gBACzC,MAAM;QACd,CAAC;IACL,CAAC;IAED,iCAAiC;IACjC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC1C,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAE3C,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,CACxD,EAAE,IAAI,EAAE,aAAa,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAC9D,+BAAoB,EACpB;QACI,IAAI,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE;KACvB,CACJ,CAAC;IAEF,IAAI,KAAK,EAAE,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;QACtC,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,aAAa;gBACd,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;gBACpD,MAAM;YACV,KAAK,YAAY;gBACb,OAAO,CAAC,GAAG,CAAC,gBAAgB,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;gBACnD,MAAM;YACV,KAAK,QAAQ;gBACT,OAAO,CAAC,GAAG,CAAC,YAAY,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC1D,MAAM;YACV,KAAK,OAAO;gBACR,OAAO,CAAC,KAAK,CAAC,UAAU,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;gBACzC,MAAM;QACd,CAAC;IACL,CAAC;IAED,UAAU;IACV,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACtD,MAAM,SAAS,CAAC,KAAK,EAAE,CAAC;IACxB,QAAQ,CAAC,KAAK,EAAE,CAAC;AACrB,CAAC;AAED,+BAA+B;AAC/B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACnC,IAAI,GAAG,GAAG,2BAA2B,CAAC;AAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;IACnC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;QACrC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAClB,CAAC,EAAE,CAAC;IACR,CAAC;AACL,CAAC;AAED,iBAAiB;AACjB,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;IACnB,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;IAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC"}