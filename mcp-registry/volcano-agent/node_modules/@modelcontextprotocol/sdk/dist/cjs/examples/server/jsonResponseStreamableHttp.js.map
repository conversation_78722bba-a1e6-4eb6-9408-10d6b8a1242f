{"version": 3, "file": "jsonResponseStreamableHttp.js", "sourceRoot": "", "sources": ["../../../../src/examples/server/jsonResponseStreamableHttp.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,6CAAyC;AACzC,gDAAgD;AAChD,sEAA+E;AAC/E,0CAA4B;AAC5B,6CAAqE;AACrE,wDAA8D;AAE9D,mDAAmD;AACnD,MAAM,SAAS,GAAG,GAAG,EAAE;IACnB,MAAM,MAAM,GAAG,IAAI,kBAAS,CACxB;QACI,IAAI,EAAE,sCAAsC;QAC5C,OAAO,EAAE,OAAO;KACnB,EACD;QACI,YAAY,EAAE;YACV,OAAO,EAAE,EAAE;SACd;KACJ,CACJ,CAAC;IAEF,iDAAiD;IACjD,MAAM,CAAC,YAAY,CACf,OAAO,EACP;QACI,WAAW,EAAE,wBAAwB;QACrC,WAAW,EAAE;YACT,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC;SAC7C;KACJ,EACD,KAAK,EAAE,EAAE,IAAI,EAAE,EAA2B,EAAE;QACxC,OAAO;YACH,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,UAAU,IAAI,GAAG;iBAC1B;aACJ;SACJ,CAAC;IACN,CAAC,CACJ,CAAC;IAEF,mEAAmE;IACnE,MAAM,CAAC,YAAY,CACf,aAAa,EACb;QACI,WAAW,EAAE,gEAAgE;QAC7E,WAAW,EAAE;YACT,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC;SAC7C;KACJ,EACD,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,KAAK,EAA2B,EAAE;QAC/C,MAAM,KAAK,GAAG,CAAC,EAAU,EAAE,EAAE,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QAE9E,MAAM,MAAM,CAAC,kBAAkB,CAC3B;YACI,KAAK,EAAE,OAAO;YACd,IAAI,EAAE,4BAA4B,IAAI,EAAE;SAC3C,EACD,KAAK,CAAC,SAAS,CAClB,CAAC;QAEF,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,sCAAsC;QAEzD,MAAM,MAAM,CAAC,kBAAkB,CAC3B;YACI,KAAK,EAAE,MAAM;YACb,IAAI,EAAE,6BAA6B,IAAI,EAAE;SAC5C,EACD,KAAK,CAAC,SAAS,CAClB,CAAC;QAEF,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,6CAA6C;QAEhE,MAAM,MAAM,CAAC,kBAAkB,CAC3B;YACI,KAAK,EAAE,MAAM;YACb,IAAI,EAAE,8BAA8B,IAAI,EAAE;SAC7C,EACD,KAAK,CAAC,SAAS,CAClB,CAAC;QAEF,OAAO;YACH,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,iBAAiB,IAAI,GAAG;iBACjC;aACJ;SACJ,CAAC;IACN,CAAC,CACJ,CAAC;IACF,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,MAAM,GAAG,GAAG,IAAA,gCAAmB,GAAE,CAAC;AAElC,wCAAwC;AACxC,MAAM,UAAU,GAA2D,EAAE,CAAC;AAE9E,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACnD,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/C,IAAI,CAAC;QACD,gCAAgC;QAChC,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAuB,CAAC;QACtE,IAAI,SAAwC,CAAC;QAE7C,IAAI,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,2BAA2B;YAC3B,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;aAAM,IAAI,CAAC,SAAS,IAAI,IAAA,8BAAmB,EAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,sDAAsD;YACtD,SAAS,GAAG,IAAI,iDAA6B,CAAC;gBAC1C,kBAAkB,EAAE,GAAG,EAAE,CAAC,IAAA,wBAAU,GAAE;gBACtC,kBAAkB,EAAE,IAAI,EAAE,4BAA4B;gBACtD,oBAAoB,EAAE,SAAS,CAAC,EAAE;oBAC9B,gEAAgE;oBAChE,wFAAwF;oBACxF,OAAO,CAAC,GAAG,CAAC,gCAAgC,SAAS,EAAE,CAAC,CAAC;oBACzD,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;gBACtC,CAAC;aACJ,CAAC,CAAC;YAEH,sEAAsE;YACtE,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;YAC3B,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAChC,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;YAClD,OAAO,CAAC,kBAAkB;QAC9B,CAAC;aAAM,CAAC;YACJ,gEAAgE;YAChE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACjB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACH,IAAI,EAAE,CAAC,KAAK;oBACZ,OAAO,EAAE,2CAA2C;iBACvD;gBACD,EAAE,EAAE,IAAI;aACX,CAAC,CAAC;YACH,OAAO;QACX,CAAC;QAED,oEAAoE;QACpE,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;YACnB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACjB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACH,IAAI,EAAE,CAAC,KAAK;oBACZ,OAAO,EAAE,uBAAuB;iBACnC;gBACD,EAAE,EAAE,IAAI;aACX,CAAC,CAAC;QACP,CAAC;IACL,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,wDAAwD;AACxD,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAClD,qFAAqF;IACrF,kEAAkE;IAClE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;AACpE,CAAC,CAAC,CAAC;AAEH,mBAAmB;AACnB,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;IACrB,IAAI,KAAK,EAAE,CAAC;QACR,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,gDAAgD,IAAI,EAAE,CAAC,CAAC;AACxE,CAAC,CAAC,CAAC;AAEH,yBAAyB;AACzB,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;IAC5B,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IACvC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC"}