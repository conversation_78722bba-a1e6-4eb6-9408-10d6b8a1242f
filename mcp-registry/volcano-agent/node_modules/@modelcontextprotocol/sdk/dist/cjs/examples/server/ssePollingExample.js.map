{"version": 3, "file": "ssePollingExample.js", "sourceRoot": "", "sources": ["../../../../src/examples/server/ssePollingExample.ts"], "names": [], "mappings": ";;;;;AAeA,6CAAyC;AACzC,gDAAgD;AAChD,wDAA8D;AAC9D,sEAA+E;AAE/E,2EAAqE;AACrE,gDAAwB;AAExB,wBAAwB;AACxB,MAAM,MAAM,GAAG,IAAI,kBAAS,CACxB;IACI,IAAI,EAAE,qBAAqB;IAC3B,OAAO,EAAE,OAAO;CACnB,EACD;IACI,YAAY,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;CAChC,CACJ,CAAC;AAEF,6EAA6E;AAC7E,MAAM,CAAC,IAAI,CACP,WAAW,EACX,0GAA0G,EAC1G,EAAE,EACF,KAAK,EAAE,KAAK,EAAE,KAAK,EAA2B,EAAE;IAC5C,MAAM,KAAK,GAAG,CAAC,EAAU,EAAE,EAAE,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IAE9E,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,SAAS,yBAAyB,CAAC,CAAC;IAE1D,mCAAmC;IACnC,MAAM,MAAM,CAAC,kBAAkB,CAC3B;QACI,KAAK,EAAE,MAAM;QACb,IAAI,EAAE,kCAAkC;KAC3C,EACD,KAAK,CAAC,SAAS,CAClB,CAAC;IACF,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;IAElB,oCAAoC;IACpC,MAAM,MAAM,CAAC,kBAAkB,CAC3B;QACI,KAAK,EAAE,MAAM;QACb,IAAI,EAAE,kCAAkC;KAC3C,EACD,KAAK,CAAC,SAAS,CAClB,CAAC;IACF,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;IAElB,4DAA4D;IAC5D,uFAAuF;IACvF,8EAA8E;IAC9E,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,SAAS,mDAAmD,CAAC,CAAC;QACpF,KAAK,CAAC,cAAc,EAAE,CAAC;IAC3B,CAAC;IAED,mDAAmD;IACnD,oEAAoE;IACpE,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;IACjB,MAAM,MAAM,CAAC,kBAAkB,CAC3B;QACI,KAAK,EAAE,MAAM;QACb,IAAI,EAAE,iEAAiE;KAC1E,EACD,KAAK,CAAC,SAAS,CAClB,CAAC;IAEF,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;IACjB,MAAM,MAAM,CAAC,kBAAkB,CAC3B;QACI,KAAK,EAAE,MAAM;QACb,IAAI,EAAE,4BAA4B;KACrC,EACD,KAAK,CAAC,SAAS,CAClB,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,SAAS,iBAAiB,CAAC,CAAC;IAElD,OAAO;QACH,OAAO,EAAE;YACL;gBACI,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,mCAAmC;aAC5C;SACJ;KACJ,CAAC;AACN,CAAC,CACJ,CAAC;AAEF,qBAAqB;AACrB,MAAM,GAAG,GAAG,IAAA,gCAAmB,GAAE,CAAC;AAClC,GAAG,CAAC,GAAG,CAAC,IAAA,cAAI,GAAE,CAAC,CAAC;AAEhB,sCAAsC;AACtC,MAAM,UAAU,GAAG,IAAI,0CAAkB,EAAE,CAAC;AAE5C,mDAAmD;AACnD,MAAM,UAAU,GAAG,IAAI,GAAG,EAAyC,CAAC;AAEpE,0BAA0B;AAC1B,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAClD,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAuB,CAAC;IAEtE,6CAA6C;IAC7C,IAAI,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAElE,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,SAAS,GAAG,IAAI,iDAA6B,CAAC;YAC1C,kBAAkB,EAAE,GAAG,EAAE,CAAC,IAAA,wBAAU,GAAE;YACtC,UAAU;YACV,aAAa,EAAE,IAAI,EAAE,4CAA4C;YACjE,oBAAoB,EAAE,EAAE,CAAC,EAAE;gBACvB,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;gBAC3C,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,SAAU,CAAC,CAAC;YACnC,CAAC;SACJ,CAAC,CAAC;QAEH,0CAA0C;QAC1C,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC;AAEH,mBAAmB;AACnB,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE;IAClB,OAAO,CAAC,GAAG,CAAC,0DAA0D,IAAI,MAAM,CAAC,CAAC;IAClF,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChB,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC;IAC7E,OAAO,CAAC,GAAG,CAAC,oEAAoE,CAAC,CAAC;IAClF,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChB,OAAO,CAAC,GAAG,CAAC,gFAAgF,CAAC,CAAC;AAClG,CAAC,CAAC,CAAC"}