{"version": 3, "file": "mcp-server.d.ts", "sourceRoot": "", "sources": ["../../../../src/experimental/tasks/mcp-server.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,KAAK,EAAE,SAAS,EAAE,cAAc,EAAkB,MAAM,qBAAqB,CAAC;AACrF,OAAO,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AAC/E,OAAO,KAAK,EAAE,eAAe,EAAiB,MAAM,gBAAgB,CAAC;AACrE,OAAO,KAAK,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AAoB1E;;;;;;;;;GASG;AACH,qBAAa,0BAA0B;IACvB,OAAO,CAAC,QAAQ,CAAC,UAAU;gBAAV,UAAU,EAAE,SAAS;IAElD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACH,gBAAgB,CAAC,UAAU,SAAS,SAAS,GAAG,iBAAiB,GAAG,SAAS,EACzE,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE;QACJ,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,YAAY,CAAC,EAAE,UAAU,CAAC;QAC1B,WAAW,CAAC,EAAE,eAAe,CAAC;QAC9B,SAAS,CAAC,EAAE,iBAAiB,CAAC;QAC9B,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KACnC,EACD,OAAO,EAAE,eAAe,CAAC,SAAS,CAAC,GACpC,cAAc;IAEjB,gBAAgB,CAAC,SAAS,SAAS,iBAAiB,GAAG,SAAS,EAAE,UAAU,SAAS,SAAS,GAAG,iBAAiB,GAAG,SAAS,EAC1H,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE;QACJ,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,WAAW,EAAE,SAAS,CAAC;QACvB,YAAY,CAAC,EAAE,UAAU,CAAC;QAC1B,WAAW,CAAC,EAAE,eAAe,CAAC;QAC9B,SAAS,CAAC,EAAE,iBAAiB,CAAC;QAC9B,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KACnC,EACD,OAAO,EAAE,eAAe,CAAC,SAAS,CAAC,GACpC,cAAc;CAsCpB"}