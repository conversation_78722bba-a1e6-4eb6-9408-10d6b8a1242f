"use strict";
/**
 * Re-exports of task-related types from the MCP protocol spec.
 * WARNING: These APIs are experimental and may change without notice.
 *
 * These types are defined in types.ts (matching the protocol spec) and
 * re-exported here for convenience when working with experimental task features.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerTasksCapabilitySchema = exports.ClientTasksCapabilitySchema = exports.CancelTaskResultSchema = exports.CancelTaskRequestSchema = exports.ListTasksResultSchema = exports.ListTasksRequestSchema = exports.GetTaskPayloadRequestSchema = exports.GetTaskResultSchema = exports.GetTaskRequestSchema = exports.TaskStatusNotificationSchema = exports.TaskStatusNotificationParamsSchema = exports.CreateTaskResultSchema = exports.TaskSchema = exports.RelatedTaskMetadataSchema = exports.TaskCreationParamsSchema = void 0;
// Task schemas (Zod)
var types_js_1 = require("../../types.js");
Object.defineProperty(exports, "TaskCreationParamsSchema", { enumerable: true, get: function () { return types_js_1.TaskCreationParamsSchema; } });
Object.defineProperty(exports, "RelatedTaskMetadataSchema", { enumerable: true, get: function () { return types_js_1.RelatedTaskMetadataSchema; } });
Object.defineProperty(exports, "TaskSchema", { enumerable: true, get: function () { return types_js_1.TaskSchema; } });
Object.defineProperty(exports, "CreateTaskResultSchema", { enumerable: true, get: function () { return types_js_1.CreateTaskResultSchema; } });
Object.defineProperty(exports, "TaskStatusNotificationParamsSchema", { enumerable: true, get: function () { return types_js_1.TaskStatusNotificationParamsSchema; } });
Object.defineProperty(exports, "TaskStatusNotificationSchema", { enumerable: true, get: function () { return types_js_1.TaskStatusNotificationSchema; } });
Object.defineProperty(exports, "GetTaskRequestSchema", { enumerable: true, get: function () { return types_js_1.GetTaskRequestSchema; } });
Object.defineProperty(exports, "GetTaskResultSchema", { enumerable: true, get: function () { return types_js_1.GetTaskResultSchema; } });
Object.defineProperty(exports, "GetTaskPayloadRequestSchema", { enumerable: true, get: function () { return types_js_1.GetTaskPayloadRequestSchema; } });
Object.defineProperty(exports, "ListTasksRequestSchema", { enumerable: true, get: function () { return types_js_1.ListTasksRequestSchema; } });
Object.defineProperty(exports, "ListTasksResultSchema", { enumerable: true, get: function () { return types_js_1.ListTasksResultSchema; } });
Object.defineProperty(exports, "CancelTaskRequestSchema", { enumerable: true, get: function () { return types_js_1.CancelTaskRequestSchema; } });
Object.defineProperty(exports, "CancelTaskResultSchema", { enumerable: true, get: function () { return types_js_1.CancelTaskResultSchema; } });
Object.defineProperty(exports, "ClientTasksCapabilitySchema", { enumerable: true, get: function () { return types_js_1.ClientTasksCapabilitySchema; } });
Object.defineProperty(exports, "ServerTasksCapabilitySchema", { enumerable: true, get: function () { return types_js_1.ServerTasksCapabilitySchema; } });
//# sourceMappingURL=types.js.map