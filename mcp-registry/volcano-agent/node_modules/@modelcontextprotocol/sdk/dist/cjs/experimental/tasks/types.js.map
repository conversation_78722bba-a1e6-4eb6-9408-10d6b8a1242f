{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../../src/experimental/tasks/types.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAEH,qBAAqB;AACrB,2CAgBwB;AAfpB,oHAAA,wBAAwB,OAAA;AACxB,qHAAA,yBAAyB,OAAA;AACzB,sGAAA,UAAU,OAAA;AACV,kHAAA,sBAAsB,OAAA;AACtB,8HAAA,kCAAkC,OAAA;AAClC,wHAAA,4BAA4B,OAAA;AAC5B,gHAAA,oBAAoB,OAAA;AACpB,+GAAA,mBAAmB,OAAA;AACnB,uHAAA,2BAA2B,OAAA;AAC3B,kHAAA,sBAAsB,OAAA;AACtB,iHAAA,qBAAqB,OAAA;AACrB,mHAAA,uBAAuB,OAAA;AACvB,kHAAA,sBAAsB,OAAA;AACtB,uHAAA,2BAA2B,OAAA;AAC3B,uHAAA,2BAA2B,OAAA"}