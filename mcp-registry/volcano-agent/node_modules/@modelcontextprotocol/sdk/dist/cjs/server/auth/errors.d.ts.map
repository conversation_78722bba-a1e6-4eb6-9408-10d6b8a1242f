{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../../../src/server/auth/errors.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAE1D;;GAEG;AACH,qBAAa,UAAW,SAAQ,KAAK;aAKb,QAAQ,CAAC,EAAE,MAAM;IAJrC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC;gBAGrB,OAAO,EAAE,MAAM,EACC,QAAQ,CAAC,EAAE,MAAM,YAAA;IAMrC;;OAEG;IACH,gBAAgB,IAAI,kBAAkB;IAatC,IAAI,SAAS,IAAI,MAAM,CAEtB;CACJ;AAED;;;;GAIG;AACH,qBAAa,mBAAoB,SAAQ,UAAU;IAC/C,MAAM,CAAC,SAAS,SAAqB;CACxC;AAED;;;GAGG;AACH,qBAAa,kBAAmB,SAAQ,UAAU;IAC9C,MAAM,CAAC,SAAS,SAAoB;CACvC;AAED;;;;GAIG;AACH,qBAAa,iBAAkB,SAAQ,UAAU;IAC7C,MAAM,CAAC,SAAS,SAAmB;CACtC;AAED;;;GAGG;AACH,qBAAa,uBAAwB,SAAQ,UAAU;IACnD,MAAM,CAAC,SAAS,SAAyB;CAC5C;AAED;;;GAGG;AACH,qBAAa,yBAA0B,SAAQ,UAAU;IACrD,MAAM,CAAC,SAAS,SAA4B;CAC/C;AAED;;;GAGG;AACH,qBAAa,iBAAkB,SAAQ,UAAU;IAC7C,MAAM,CAAC,SAAS,SAAmB;CACtC;AAED;;GAEG;AACH,qBAAa,iBAAkB,SAAQ,UAAU;IAC7C,MAAM,CAAC,SAAS,SAAmB;CACtC;AAED;;;GAGG;AACH,qBAAa,WAAY,SAAQ,UAAU;IACvC,MAAM,CAAC,SAAS,SAAkB;CACrC;AAED;;;GAGG;AACH,qBAAa,2BAA4B,SAAQ,UAAU;IACvD,MAAM,CAAC,SAAS,SAA6B;CAChD;AAED;;;GAGG;AACH,qBAAa,4BAA6B,SAAQ,UAAU;IACxD,MAAM,CAAC,SAAS,SAA+B;CAClD;AAED;;;GAGG;AACH,qBAAa,yBAA0B,SAAQ,UAAU;IACrD,MAAM,CAAC,SAAS,SAA4B;CAC/C;AAED;;;GAGG;AACH,qBAAa,iBAAkB,SAAQ,UAAU;IAC7C,MAAM,CAAC,SAAS,SAAmB;CACtC;AAED;;;GAGG;AACH,qBAAa,qBAAsB,SAAQ,UAAU;IACjD,MAAM,CAAC,SAAS,SAAwB;CAC3C;AAED;;;GAGG;AACH,qBAAa,oBAAqB,SAAQ,UAAU;IAChD,MAAM,CAAC,SAAS,SAAuB;CAC1C;AAED;;;GAGG;AACH,qBAAa,0BAA2B,SAAQ,UAAU;IACtD,MAAM,CAAC,SAAS,SAA6B;CAChD;AAED;;GAEG;AACH,qBAAa,sBAAuB,SAAQ,UAAU;IAClD,MAAM,CAAC,SAAS,SAAwB;CAC3C;AAED;;;GAGG;AACH,qBAAa,kBAAmB,SAAQ,UAAU;IAC9C,MAAM,CAAC,SAAS,SAAoB;CACvC;AAED;;GAEG;AACH,qBAAa,gBAAiB,SAAQ,UAAU;IAExC,OAAO,CAAC,QAAQ,CAAC,eAAe;gBAAf,eAAe,EAAE,MAAM,EACxC,OAAO,EAAE,MAAM,EACf,QAAQ,CAAC,EAAE,MAAM;IAKrB,IAAI,SAAS,IAAI,MAAM,CAEtB;CACJ;AAED;;GAEG;AACH,eAAO,MAAM,YAAY;;CAkBf,CAAC"}