{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../../../src/server/auth/errors.ts"], "names": [], "mappings": ";;;AAEA;;GAEG;AACH,MAAa,UAAW,SAAQ,KAAK;IAGjC,YACI,OAAe,EACC,QAAiB;QAEjC,KAAK,CAAC,OAAO,CAAC,CAAC;QAFC,aAAQ,GAAR,QAAQ,CAAS;QAGjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACZ,MAAM,QAAQ,GAAuB;YACjC,KAAK,EAAE,IAAI,CAAC,SAAS;YACrB,iBAAiB,EAAE,IAAI,CAAC,OAAO;SAClC,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;QACvC,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,IAAI,SAAS;QACT,OAAQ,IAAI,CAAC,WAAiC,CAAC,SAAS,CAAC;IAC7D,CAAC;CACJ;AA9BD,gCA8BC;AAED;;;;GAIG;AACH,MAAa,mBAAoB,SAAQ,UAAU;;AAAnD,kDAEC;AADU,6BAAS,GAAG,iBAAiB,CAAC;AAGzC;;;GAGG;AACH,MAAa,kBAAmB,SAAQ,UAAU;;AAAlD,gDAEC;AADU,4BAAS,GAAG,gBAAgB,CAAC;AAGxC;;;;GAIG;AACH,MAAa,iBAAkB,SAAQ,UAAU;;AAAjD,8CAEC;AADU,2BAAS,GAAG,eAAe,CAAC;AAGvC;;;GAGG;AACH,MAAa,uBAAwB,SAAQ,UAAU;;AAAvD,0DAEC;AADU,iCAAS,GAAG,qBAAqB,CAAC;AAG7C;;;GAGG;AACH,MAAa,yBAA0B,SAAQ,UAAU;;AAAzD,8DAEC;AADU,mCAAS,GAAG,wBAAwB,CAAC;AAGhD;;;GAGG;AACH,MAAa,iBAAkB,SAAQ,UAAU;;AAAjD,8CAEC;AADU,2BAAS,GAAG,eAAe,CAAC;AAGvC;;GAEG;AACH,MAAa,iBAAkB,SAAQ,UAAU;;AAAjD,8CAEC;AADU,2BAAS,GAAG,eAAe,CAAC;AAGvC;;;GAGG;AACH,MAAa,WAAY,SAAQ,UAAU;;AAA3C,kCAEC;AADU,qBAAS,GAAG,cAAc,CAAC;AAGtC;;;GAGG;AACH,MAAa,2BAA4B,SAAQ,UAAU;;AAA3D,kEAEC;AADU,qCAAS,GAAG,yBAAyB,CAAC;AAGjD;;;GAGG;AACH,MAAa,4BAA6B,SAAQ,UAAU;;AAA5D,oEAEC;AADU,sCAAS,GAAG,2BAA2B,CAAC;AAGnD;;;GAGG;AACH,MAAa,yBAA0B,SAAQ,UAAU;;AAAzD,8DAEC;AADU,mCAAS,GAAG,wBAAwB,CAAC;AAGhD;;;GAGG;AACH,MAAa,iBAAkB,SAAQ,UAAU;;AAAjD,8CAEC;AADU,2BAAS,GAAG,eAAe,CAAC;AAGvC;;;GAGG;AACH,MAAa,qBAAsB,SAAQ,UAAU;;AAArD,sDAEC;AADU,+BAAS,GAAG,oBAAoB,CAAC;AAG5C;;;GAGG;AACH,MAAa,oBAAqB,SAAQ,UAAU;;AAApD,oDAEC;AADU,8BAAS,GAAG,mBAAmB,CAAC;AAG3C;;;GAGG;AACH,MAAa,0BAA2B,SAAQ,UAAU;;AAA1D,gEAEC;AADU,oCAAS,GAAG,yBAAyB,CAAC;AAGjD;;GAEG;AACH,MAAa,sBAAuB,SAAQ,UAAU;;AAAtD,wDAEC;AADU,gCAAS,GAAG,oBAAoB,CAAC;AAG5C;;;GAGG;AACH,MAAa,kBAAmB,SAAQ,UAAU;;AAAlD,gDAEC;AADU,4BAAS,GAAG,gBAAgB,CAAC;AAGxC;;GAEG;AACH,MAAa,gBAAiB,SAAQ,UAAU;IAC5C,YACqB,eAAuB,EACxC,OAAe,EACf,QAAiB;QAEjB,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAJR,oBAAe,GAAf,eAAe,CAAQ;IAK5C,CAAC;IAED,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;CACJ;AAZD,4CAYC;AAED;;GAEG;AACU,QAAA,YAAY,GAAG;IACxB,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,mBAAmB;IACpD,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,kBAAkB;IAClD,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,uBAAuB,CAAC,SAAS,CAAC,EAAE,uBAAuB;IAC5D,CAAC,yBAAyB,CAAC,SAAS,CAAC,EAAE,yBAAyB;IAChE,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,WAAW;IACpC,CAAC,2BAA2B,CAAC,SAAS,CAAC,EAAE,2BAA2B;IACpE,CAAC,4BAA4B,CAAC,SAAS,CAAC,EAAE,4BAA4B;IACtE,CAAC,yBAAyB,CAAC,SAAS,CAAC,EAAE,yBAAyB;IAChE,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,qBAAqB;IACxD,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE,oBAAoB;IACtD,CAAC,0BAA0B,CAAC,SAAS,CAAC,EAAE,0BAA0B;IAClE,CAAC,sBAAsB,CAAC,SAAS,CAAC,EAAE,sBAAsB;IAC1D,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,kBAAkB;CAC5C,CAAC"}