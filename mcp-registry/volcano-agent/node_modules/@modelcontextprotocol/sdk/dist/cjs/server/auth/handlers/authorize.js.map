{"version": 3, "file": "authorize.js", "sourceRoot": "", "sources": ["../../../../../src/server/auth/handlers/authorize.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,oDAgHC;AAnJD,0CAA4B;AAC5B,sDAA8B;AAE9B,2DAA4E;AAC5E,uEAAiE;AACjE,4CAAsH;AAWtH,iEAAiE;AACjE,MAAM,+BAA+B,GAAG,CAAC,CAAC,MAAM,CAAC;IAC7C,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE;IACrB,YAAY,EAAE,CAAC;SACV,MAAM,EAAE;SACR,QAAQ,EAAE;SACV,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;CACpH,CAAC,CAAC;AAEH,yHAAyH;AACzH,MAAM,gCAAgC,GAAG,CAAC,CAAC,MAAM,CAAC;IAC9C,aAAa,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IAChC,cAAc,EAAE,CAAC,CAAC,MAAM,EAAE;IAC1B,qBAAqB,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IACxC,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;CACxC,CAAC,CAAC;AAEH,SAAgB,oBAAoB,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,eAAe,EAA+B;IACtG,sCAAsC;IACtC,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;IAChC,MAAM,CAAC,GAAG,CAAC,IAAA,kCAAc,EAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAM,CAAC,GAAG,CAAC,iBAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAEpD,iDAAiD;IACjD,IAAI,eAAe,KAAK,KAAK,EAAE,CAAC;QAC5B,MAAM,CAAC,GAAG,CACN,IAAA,8BAAS,EAAC;YACN,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;YACvC,GAAG,EAAE,GAAG,EAAE,4BAA4B;YACtC,eAAe,EAAE,IAAI;YACrB,aAAa,EAAE,KAAK;YACpB,OAAO,EAAE,IAAI,gCAAoB,CAAC,6DAA6D,CAAC,CAAC,gBAAgB,EAAE;YACnH,GAAG,eAAe;SACrB,CAAC,CACL,CAAC;IACN,CAAC;IAED,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;QAC/B,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;QAE3C,mEAAmE;QACnE,oDAAoD;QACpD,2DAA2D;QAE3D,0FAA0F;QAC1F,IAAI,SAAS,EAAE,YAAY,EAAE,MAAM,CAAC;QACpC,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,+BAA+B,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACvG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAClB,MAAM,IAAI,+BAAmB,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxD,CAAC;YAED,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAClC,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;YAExC,MAAM,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAC1D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,MAAM,IAAI,8BAAkB,CAAC,mBAAmB,CAAC,CAAC;YACtD,CAAC;YAED,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC/C,MAAM,IAAI,+BAAmB,CAAC,2BAA2B,CAAC,CAAC;gBAC/D,CAAC;YACL,CAAC;iBAAM,IAAI,MAAM,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3C,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,+BAAmB,CAAC,yEAAyE,CAAC,CAAC;YAC7G,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,+CAA+C;YAC/C,EAAE;YACF,yEAAyE;YACzE,uEAAuE;YACvE,uEAAuE;YACvE,eAAe;YACf,IAAI,KAAK,YAAY,sBAAU,EAAE,CAAC;gBAC9B,MAAM,MAAM,GAAG,KAAK,YAAY,uBAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBACxD,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACJ,MAAM,WAAW,GAAG,IAAI,uBAAW,CAAC,uBAAuB,CAAC,CAAC;gBAC7D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACzD,CAAC;YAED,OAAO;QACX,CAAC;QAED,yFAAyF;QACzF,IAAI,KAAK,CAAC;QACV,IAAI,CAAC;YACD,8CAA8C;YAC9C,MAAM,WAAW,GAAG,gCAAgC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC7G,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBACvB,MAAM,IAAI,+BAAmB,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC7D,CAAC;YAED,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC;YAC7D,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;YAE/B,kBAAkB;YAClB,IAAI,eAAe,GAAa,EAAE,CAAC;YACnC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACtB,eAAe,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACvC,CAAC;YAED,oDAAoD;YACpD,MAAM,QAAQ,CAAC,SAAS,CACpB,MAAM,EACN;gBACI,KAAK;gBACL,MAAM,EAAE,eAAe;gBACvB,WAAW,EAAE,YAAY;gBACzB,aAAa,EAAE,cAAc;gBAC7B,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS;aACrD,EACD,GAAG,CACN,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wDAAwD;YACxD,IAAI,KAAK,YAAY,sBAAU,EAAE,CAAC;gBAC9B,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,mBAAmB,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YACvE,CAAC;iBAAM,CAAC;gBACJ,MAAM,WAAW,GAAG,IAAI,uBAAW,CAAC,uBAAuB,CAAC,CAAC;gBAC7D,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,mBAAmB,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;YAC7E,CAAC;QACL,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,WAAmB,EAAE,KAAiB,EAAE,KAAc;IAC/E,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC;IACtC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;IACpD,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAC9D,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;QACjB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC3D,CAAC;IACD,IAAI,KAAK,EAAE,CAAC;QACR,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IACD,OAAO,QAAQ,CAAC,IAAI,CAAC;AACzB,CAAC"}