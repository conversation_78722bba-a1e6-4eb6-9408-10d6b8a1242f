{"version": 3, "file": "proxyProvider.js", "sourceRoot": "", "sources": ["../../../../../src/server/auth/providers/proxyProvider.ts"], "names": [], "mappings": ";;;AAEA,qDAMiC;AAGjC,4CAA2C;AAgC3C;;GAEG;AACH,MAAa,wBAAwB;IAUjC,YAAY,OAAqB;QAJjC,4BAAuB,GAAG,IAAI,CAAC;QAK3B,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACpD,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;QAC5B,IAAI,OAAO,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;YACnC,IAAI,CAAC,WAAW,GAAG,KAAK,EAAE,MAAkC,EAAE,OAAoC,EAAE,EAAE;gBAClG,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;gBAEpD,IAAI,CAAC,aAAa,EAAE,CAAC;oBACjB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;gBACzD,CAAC;gBAED,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;gBACrC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;gBACnC,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC1C,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;oBACvB,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;gBACtD,CAAC;gBACD,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;oBAC1B,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;gBAC3D,CAAC;gBAED,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,aAAa,EAAE;oBACzD,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE;wBACL,cAAc,EAAE,mCAAmC;qBACtD;oBACD,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE;iBAC1B,CAAC,CAAC;gBACH,MAAM,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;gBAE9B,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;oBACf,MAAM,IAAI,uBAAW,CAAC,4BAA4B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;gBACzE,CAAC;YACL,CAAC,CAAC;QACN,CAAC;IACL,CAAC;IAED,IAAI,YAAY;QACZ,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;QACxD,OAAO;YACH,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,GAAG,CAAC,eAAe,IAAI;gBACnB,cAAc,EAAE,KAAK,EAAE,MAAkC,EAAE,EAAE;oBACzD,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,eAAe,EAAE;wBAC3D,MAAM,EAAE,MAAM;wBACd,OAAO,EAAE;4BACL,cAAc,EAAE,kBAAkB;yBACrC;wBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;qBAC/B,CAAC,CAAC;oBAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;wBACf,MAAM,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;wBAC9B,MAAM,IAAI,uBAAW,CAAC,+BAA+B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC5E,CAAC;oBAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACnC,OAAO,0CAAgC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACxD,CAAC;aACJ,CAAC;SACL,CAAC;IACN,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,MAAkC,EAAE,MAA2B,EAAE,GAAa;QAC1F,uCAAuC;QACvC,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;QAC5D,MAAM,YAAY,GAAG,IAAI,eAAe,CAAC;YACrC,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,aAAa,EAAE,MAAM;YACrB,YAAY,EAAE,MAAM,CAAC,WAAW;YAChC,cAAc,EAAE,MAAM,CAAC,aAAa;YACpC,qBAAqB,EAAE,MAAM;SAChC,CAAC,CAAC;QAEH,yCAAyC;QACzC,IAAI,MAAM,CAAC,KAAK;YAAE,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QAC1D,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM;YAAE,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9E,IAAI,MAAM,CAAC,QAAQ;YAAE,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAExE,SAAS,CAAC,MAAM,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC3C,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,6BAA6B,CAAC,OAAmC,EAAE,kBAA0B;QAC/F,gEAAgE;QAChE,8EAA8E;QAC9E,OAAO,EAAE,CAAC;IACd,CAAC;IAED,KAAK,CAAC,yBAAyB,CAC3B,MAAkC,EAClC,iBAAyB,EACzB,YAAqB,EACrB,WAAoB,EACpB,QAAc;QAEd,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;YAC/B,UAAU,EAAE,oBAAoB;YAChC,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,IAAI,EAAE,iBAAiB;SAC1B,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YACd,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACX,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;YACpE,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACL,cAAc,EAAE,mCAAmC;aACtD;YACD,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE;SAC1B,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACf,MAAM,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;YAC9B,MAAM,IAAI,uBAAW,CAAC,0BAA0B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACnC,OAAO,2BAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,oBAAoB,CACtB,MAAkC,EAClC,YAAoB,EACpB,MAAiB,EACjB,QAAc;QAEd,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;YAC/B,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,aAAa,EAAE,YAAY;SAC9B,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACvB,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,MAAM,EAAE,MAAM,EAAE,CAAC;YACjB,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACX,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;YACpE,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACL,cAAc,EAAE,mCAAmC;aACtD;YACD,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE;SAC1B,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACf,MAAM,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;YAC9B,MAAM,IAAI,uBAAW,CAAC,yBAAyB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACnC,OAAO,2BAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,KAAa;QACjC,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;CACJ;AA/LD,4DA+LC"}