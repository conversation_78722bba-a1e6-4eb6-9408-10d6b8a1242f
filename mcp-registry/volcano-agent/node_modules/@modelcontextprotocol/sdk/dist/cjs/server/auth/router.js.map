{"version": 3, "file": "router.js", "sourceRoot": "", "sources": ["../../../../src/server/auth/router.ts"], "names": [], "mappings": ";;;;;;AAgIA,sCAyCC;AA8BD,sDAuBC;AAaD,oFAIC;AA/OD,sDAAkD;AAClD,wDAAqG;AACrG,kDAAwE;AACxE,0DAA4F;AAC5F,oDAAmF;AACnF,wDAAyD;AAIzD,sFAAsF;AACtF,MAAM,sBAAsB,GACxB,OAAO,CAAC,GAAG,CAAC,yCAAyC,KAAK,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,yCAAyC,KAAK,GAAG,CAAC;AACtI,IAAI,sBAAsB,EAAE,CAAC;IACzB,sCAAsC;IACtC,OAAO,CAAC,IAAI,CAAC,gHAAgH,CAAC,CAAC;AACnI,CAAC;AAgDD,MAAM,cAAc,GAAG,CAAC,MAAW,EAAQ,EAAE;IACzC,mHAAmH;IACnH,IAAI,MAAM,CAAC,QAAQ,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,KAAK,WAAW,IAAI,MAAM,CAAC,QAAQ,KAAK,WAAW,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChI,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChD,CAAC;IACD,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,KAAK,CAAC,wCAAwC,MAAM,EAAE,CAAC,CAAC;IACtE,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;QAChB,MAAM,IAAI,KAAK,CAAC,4CAA4C,MAAM,EAAE,CAAC,CAAC;IAC1E,CAAC;AACL,CAAC,CAAC;AAEK,MAAM,mBAAmB,GAAG,CAAC,OAMnC,EAAiB,EAAE;IAChB,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC;IACjC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAEhC,cAAc,CAAC,MAAM,CAAC,CAAC;IAEvB,MAAM,sBAAsB,GAAG,YAAY,CAAC;IAC5C,MAAM,cAAc,GAAG,QAAQ,CAAC;IAChC,MAAM,qBAAqB,GAAG,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;IACrG,MAAM,mBAAmB,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;IAEjF,MAAM,QAAQ,GAAkB;QAC5B,MAAM,EAAE,MAAM,CAAC,IAAI;QACnB,qBAAqB,EAAE,OAAO,CAAC,uBAAuB,EAAE,IAAI;QAE5D,sBAAsB,EAAE,IAAI,GAAG,CAAC,sBAAsB,EAAE,OAAO,IAAI,MAAM,CAAC,CAAC,IAAI;QAC/E,wBAAwB,EAAE,CAAC,MAAM,CAAC;QAClC,gCAAgC,EAAE,CAAC,MAAM,CAAC;QAE1C,cAAc,EAAE,IAAI,GAAG,CAAC,cAAc,EAAE,OAAO,IAAI,MAAM,CAAC,CAAC,IAAI;QAC/D,qCAAqC,EAAE,CAAC,oBAAoB,EAAE,MAAM,CAAC;QACrE,qBAAqB,EAAE,CAAC,oBAAoB,EAAE,eAAe,CAAC;QAE9D,gBAAgB,EAAE,OAAO,CAAC,eAAe;QAEzC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,mBAAmB,EAAE,OAAO,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QAC3G,0CAA0C,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,SAAS;QAEpG,qBAAqB,EAAE,qBAAqB,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,qBAAqB,EAAE,OAAO,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;KACpH,CAAC;IAEF,OAAO,QAAQ,CAAC;AACpB,CAAC,CAAC;AAtCW,QAAA,mBAAmB,uBAsC9B;AAEF;;;;;;;;;;;GAWG;AACH,SAAgB,aAAa,CAAC,OAA0B;IACpD,MAAM,aAAa,GAAG,IAAA,2BAAmB,EAAC,OAAO,CAAC,CAAC;IAEnD,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;IAEhC,MAAM,CAAC,GAAG,CACN,IAAI,GAAG,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,QAAQ,EACtD,IAAA,mCAAoB,EAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,oBAAoB,EAAE,CAAC,CACxF,CAAC;IAEF,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,QAAQ,EAAE,IAAA,uBAAY,EAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAElI,MAAM,CAAC,GAAG,CACN,qBAAqB,CAAC;QAClB,aAAa;QACb,sFAAsF;QACtF,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC;QAChG,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;QACxD,eAAe,EAAE,OAAO,CAAC,eAAe;QACxC,YAAY,EAAE,OAAO,CAAC,YAAY;KACrC,CAAC,CACL,CAAC;IAEF,IAAI,aAAa,CAAC,qBAAqB,EAAE,CAAC;QACtC,MAAM,CAAC,GAAG,CACN,IAAI,GAAG,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,QAAQ,EACrD,IAAA,uCAAyB,EAAC;YACtB,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY;YAC3C,GAAG,OAAO,CAAC,yBAAyB;SACvC,CAAC,CACL,CAAC;IACN,CAAC;IAED,IAAI,aAAa,CAAC,mBAAmB,EAAE,CAAC;QACpC,MAAM,CAAC,GAAG,CACN,IAAI,GAAG,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,QAAQ,EACnD,IAAA,6BAAiB,EAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAClF,CAAC;IACN,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AA8BD,SAAgB,qBAAqB,CAAC,OAA4B;IAC9D,cAAc,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAEtD,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;IAEhC,MAAM,yBAAyB,GAAmC;QAC9D,QAAQ,EAAE,OAAO,CAAC,iBAAiB,CAAC,IAAI;QAExC,qBAAqB,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC;QAErD,gBAAgB,EAAE,OAAO,CAAC,eAAe;QACzC,aAAa,EAAE,OAAO,CAAC,YAAY;QACnC,sBAAsB,EAAE,OAAO,CAAC,uBAAuB,EAAE,IAAI;KAChE,CAAC;IAEF,kDAAkD;IAClD,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC;IAChE,MAAM,CAAC,GAAG,CAAC,wCAAwC,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,IAAA,6BAAe,EAAC,yBAAyB,CAAC,CAAC,CAAC;IAE/H,uEAAuE;IACvE,MAAM,CAAC,GAAG,CAAC,yCAAyC,EAAE,IAAA,6BAAe,EAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;IAE9F,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,oCAAoC,CAAC,SAAc;IAC/D,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAClC,MAAM,MAAM,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;IAClE,OAAO,IAAI,GAAG,CAAC,wCAAwC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;AAC7E,CAAC"}