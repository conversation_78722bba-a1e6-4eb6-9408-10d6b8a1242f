{"version": 3, "file": "mcp.d.ts", "sourceRoot": "", "sources": ["../../../src/server/mcp.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AACnD,OAAO,EACH,SAAS,EACT,eAAe,EACf,iBAAiB,EACjB,YAAY,EACZ,WAAW,EASd,MAAM,iBAAiB,CAAC;AAEzB,OAAO,EACH,cAAc,EAGd,cAAc,EAOd,QAAQ,EACR,mBAAmB,EAYnB,eAAe,EACf,kBAAkB,EAClB,aAAa,EACb,kBAAkB,EAClB,eAAe,EACf,0BAA0B,EAE1B,MAAM,EAMN,aAAa,EAChB,MAAM,aAAa,CAAC;AAErB,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AAGnD,OAAO,EAAE,0BAA0B,EAAE,MAAM,qCAAqC,CAAC;AACjF,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,qCAAqC,CAAC;AAG3E;;;;GAIG;AACH,qBAAa,SAAS;IAClB;;OAEG;IACH,SAAgB,MAAM,EAAE,MAAM,CAAC;IAE/B,OAAO,CAAC,oBAAoB,CAA6C;IACzE,OAAO,CAAC,4BAA4B,CAE7B;IACP,OAAO,CAAC,gBAAgB,CAA0C;IAClE,OAAO,CAAC,kBAAkB,CAA4C;IACtE,OAAO,CAAC,aAAa,CAAC,CAAwC;gBAElD,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC,EAAE,aAAa;IAI/D;;;;;;OAMG;IACH,IAAI,YAAY,IAAI;QAAE,KAAK,EAAE,0BAA0B,CAAA;KAAE,CAOxD;IAED;;;;OAIG;IACG,OAAO,CAAC,SAAS,EAAE,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;IAIlD;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAI5B,OAAO,CAAC,wBAAwB,CAAS;IAEzC,OAAO,CAAC,sBAAsB;IAiH9B;;;;;OAKG;IACH,OAAO,CAAC,eAAe;IAYvB;;OAEG;YACW,iBAAiB;IA0B/B;;OAEG;YACW,kBAAkB;IAkChC;;OAEG;YACW,kBAAkB;IAoChC;;OAEG;YACW,0BAA0B;IAqCxC,OAAO,CAAC,6BAA6B,CAAS;IAE9C,OAAO,CAAC,2BAA2B;YA6BrB,sBAAsB;YA4BtB,wBAAwB;IAwBtC,OAAO,CAAC,4BAA4B,CAAS;IAE7C,OAAO,CAAC,0BAA0B;IA+ElC,OAAO,CAAC,0BAA0B,CAAS;IAE3C,OAAO,CAAC,wBAAwB;IA8DhC;;;OAGG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,oBAAoB,GAAG,kBAAkB;IAE3F;;;OAGG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,YAAY,EAAE,oBAAoB,GAAG,kBAAkB;IAEvH;;;OAGG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,YAAY,EAAE,4BAA4B,GAAG,0BAA0B;IAE1H;;;OAGG;IACH,QAAQ,CACJ,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,gBAAgB,EAC1B,QAAQ,EAAE,gBAAgB,EAC1B,YAAY,EAAE,4BAA4B,GAC3C,0BAA0B;IA6C7B;;;OAGG;IACH,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,YAAY,EAAE,oBAAoB,GAAG,kBAAkB;IACvI,gBAAgB,CACZ,IAAI,EAAE,MAAM,EACZ,aAAa,EAAE,gBAAgB,EAC/B,MAAM,EAAE,gBAAgB,EACxB,YAAY,EAAE,4BAA4B,GAC3C,0BAA0B;IA0C7B,OAAO,CAAC,yBAAyB;IAiCjC,OAAO,CAAC,iCAAiC;IAyCzC,OAAO,CAAC,uBAAuB;IA6C/B,OAAO,CAAC,qBAAqB;IAsD7B;;;OAGG;IACH,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,YAAY,GAAG,cAAc;IAEpD;;;OAGG;IACH,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,EAAE,YAAY,GAAG,cAAc;IAEzE;;;;;;;OAOG;IACH,IAAI,CAAC,IAAI,SAAS,iBAAiB,EAC/B,IAAI,EAAE,MAAM,EACZ,yBAAyB,EAAE,IAAI,GAAG,eAAe,EACjD,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,GACvB,cAAc;IAEjB;;;;;;;;OAQG;IACH,IAAI,CAAC,IAAI,SAAS,iBAAiB,EAC/B,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,MAAM,EACnB,yBAAyB,EAAE,IAAI,GAAG,eAAe,EACjD,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,GACvB,cAAc;IAEjB;;;OAGG;IACH,IAAI,CAAC,IAAI,SAAS,iBAAiB,EAC/B,IAAI,EAAE,MAAM,EACZ,YAAY,EAAE,IAAI,EAClB,WAAW,EAAE,eAAe,EAC5B,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,GACvB,cAAc;IAEjB;;;OAGG;IACH,IAAI,CAAC,IAAI,SAAS,iBAAiB,EAC/B,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,MAAM,EACnB,YAAY,EAAE,IAAI,EAClB,WAAW,EAAE,eAAe,EAC5B,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,GACvB,cAAc;IA4DjB;;OAEG;IACH,YAAY,CAAC,UAAU,SAAS,iBAAiB,GAAG,SAAS,EAAE,SAAS,SAAS,SAAS,GAAG,iBAAiB,GAAG,SAAS,GAAG,SAAS,EAClI,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE;QACJ,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,WAAW,CAAC,EAAE,SAAS,CAAC;QACxB,YAAY,CAAC,EAAE,UAAU,CAAC;QAC1B,WAAW,CAAC,EAAE,eAAe,CAAC;QAC9B,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KACnC,EACD,EAAE,EAAE,YAAY,CAAC,SAAS,CAAC,GAC5B,cAAc;IAoBjB;;;OAGG;IACH,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,cAAc,GAAG,gBAAgB;IAE1D;;;OAGG;IACH,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,EAAE,cAAc,GAAG,gBAAgB;IAE/E;;;OAGG;IACH,MAAM,CAAC,IAAI,SAAS,kBAAkB,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,gBAAgB;IAEnH;;;OAGG;IACH,MAAM,CAAC,IAAI,SAAS,kBAAkB,EAClC,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,MAAM,EACnB,UAAU,EAAE,IAAI,EAChB,EAAE,EAAE,cAAc,CAAC,IAAI,CAAC,GACzB,gBAAgB;IA0BnB;;OAEG;IACH,cAAc,CAAC,IAAI,SAAS,kBAAkB,EAC1C,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE;QACJ,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,UAAU,CAAC,EAAE,IAAI,CAAC;KACrB,EACD,EAAE,EAAE,cAAc,CAAC,IAAI,CAAC,GACzB,gBAAgB;IAqBnB;;;OAGG;IACH,WAAW;IAIX;;;;;;OAMG;IACG,kBAAkB,CAAC,MAAM,EAAE,0BAA0B,CAAC,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE,MAAM;IAGzF;;OAEG;IACH,uBAAuB;IAMvB;;OAEG;IACH,mBAAmB;IAMnB;;OAEG;IACH,qBAAqB;CAKxB;AAED;;GAEG;AACH,MAAM,MAAM,gCAAgC,GAAG,CAC3C,KAAK,EAAE,MAAM,EACb,OAAO,CAAC,EAAE;IACN,SAAS,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACtC,KACA,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AAElC;;;GAGG;AACH,qBAAa,gBAAgB;IAKrB,OAAO,CAAC,UAAU;IAJtB,OAAO,CAAC,YAAY,CAAc;gBAG9B,WAAW,EAAE,MAAM,GAAG,WAAW,EACzB,UAAU,EAAE;QAChB;;WAEG;QACH,IAAI,EAAE,qBAAqB,GAAG,SAAS,CAAC;QAExC;;WAEG;QACH,QAAQ,CAAC,EAAE;YACP,CAAC,QAAQ,EAAE,MAAM,GAAG,gCAAgC,CAAC;SACxD,CAAC;KACL;IAKL;;OAEG;IACH,IAAI,WAAW,IAAI,WAAW,CAE7B;IAED;;OAEG;IACH,IAAI,YAAY,IAAI,qBAAqB,GAAG,SAAS,CAEpD;IAED;;OAEG;IACH,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,gCAAgC,GAAG,SAAS;CAGnF;AAED,MAAM,MAAM,gBAAgB,CACxB,WAAW,SAAS,MAAM,EAC1B,KAAK,SAAS,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,EACpE,IAAI,SAAS,SAAS,GAAG,iBAAiB,GAAG,SAAS,IACtD,IAAI,SAAS,iBAAiB,GAC5B,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GAC7E,IAAI,SAAS,SAAS,GACpB,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GAC9E,CAAC,KAAK,EAAE,KAAK,KAAK,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;AAE7D;;;;;;;;;GASG;AACH,MAAM,MAAM,YAAY,CAAC,IAAI,SAAS,SAAS,GAAG,iBAAiB,GAAG,SAAS,GAAG,SAAS,IAAI,gBAAgB,CAC3G,cAAc,EACd,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,EACtD,IAAI,CACP,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,cAAc,CAAC,IAAI,SAAS,SAAS,GAAG,iBAAiB,GAAG,SAAS,GAAG,SAAS,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;AAE5I,MAAM,MAAM,cAAc,GAAG;IACzB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,WAAW,CAAC,EAAE,SAAS,CAAC;IACxB,YAAY,CAAC,EAAE,SAAS,CAAC;IACzB,WAAW,CAAC,EAAE,eAAe,CAAC;IAC9B,SAAS,CAAC,EAAE,aAAa,CAAC;IAC1B,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAChC,OAAO,EAAE,cAAc,CAAC,SAAS,GAAG,iBAAiB,CAAC,CAAC;IACvD,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,IAAI,IAAI,CAAC;IACf,OAAO,IAAI,IAAI,CAAC;IAChB,MAAM,CAAC,SAAS,SAAS,iBAAiB,EAAE,UAAU,SAAS,iBAAiB,EAAE,OAAO,EAAE;QACvF,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QACrB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,YAAY,CAAC,EAAE,SAAS,CAAC;QACzB,YAAY,CAAC,EAAE,UAAU,CAAC;QAC1B,WAAW,CAAC,EAAE,eAAe,CAAC;QAC9B,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAChC,QAAQ,CAAC,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;QACnC,OAAO,CAAC,EAAE,OAAO,CAAC;KACrB,GAAG,IAAI,CAAC;IACT,MAAM,IAAI,IAAI,CAAC;CAClB,CAAC;AA6EF;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC;AAE9D;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAAG,CAChC,KAAK,EAAE,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAC5D,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAExD;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,CAC/B,GAAG,EAAE,GAAG,EACR,KAAK,EAAE,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAC5D,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAEtD,MAAM,MAAM,kBAAkB,GAAG;IAC7B,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,gBAAgB,CAAC;IAC5B,YAAY,EAAE,oBAAoB,CAAC;IACnC,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,IAAI,IAAI,CAAC;IACf,OAAO,IAAI,IAAI,CAAC;IAChB,MAAM,CAAC,OAAO,EAAE;QACZ,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QACpB,QAAQ,CAAC,EAAE,gBAAgB,CAAC;QAC5B,QAAQ,CAAC,EAAE,oBAAoB,CAAC;QAChC,OAAO,CAAC,EAAE,OAAO,CAAC;KACrB,GAAG,IAAI,CAAC;IACT,MAAM,IAAI,IAAI,CAAC;CAClB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,4BAA4B,GAAG,CACvC,GAAG,EAAE,GAAG,EACR,SAAS,EAAE,SAAS,EACpB,KAAK,EAAE,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAC5D,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAEtD,MAAM,MAAM,0BAA0B,GAAG;IACrC,gBAAgB,EAAE,gBAAgB,CAAC;IACnC,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,gBAAgB,CAAC;IAC5B,YAAY,EAAE,4BAA4B,CAAC;IAC3C,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,IAAI,IAAI,CAAC;IACf,OAAO,IAAI,IAAI,CAAC;IAChB,MAAM,CAAC,OAAO,EAAE;QACZ,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QACrB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,QAAQ,CAAC,EAAE,gBAAgB,CAAC;QAC5B,QAAQ,CAAC,EAAE,gBAAgB,CAAC;QAC5B,QAAQ,CAAC,EAAE,4BAA4B,CAAC;QACxC,OAAO,CAAC,EAAE,OAAO,CAAC;KACrB,GAAG,IAAI,CAAC;IACT,MAAM,IAAI,IAAI,CAAC;CAClB,CAAC;AAEF,KAAK,kBAAkB,GAAG,iBAAiB,CAAC;AAE5C,MAAM,MAAM,cAAc,CAAC,IAAI,SAAS,SAAS,GAAG,kBAAkB,GAAG,SAAS,IAAI,IAAI,SAAS,kBAAkB,GAC/G,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAAK,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC,GACtI,CAAC,KAAK,EAAE,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAAK,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AAEpH,MAAM,MAAM,gBAAgB,GAAG;IAC3B,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,UAAU,CAAC,EAAE,eAAe,CAAC;IAC7B,QAAQ,EAAE,cAAc,CAAC,SAAS,GAAG,kBAAkB,CAAC,CAAC;IACzD,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,IAAI,IAAI,CAAC;IACf,OAAO,IAAI,IAAI,CAAC;IAChB,MAAM,CAAC,IAAI,SAAS,kBAAkB,EAAE,OAAO,EAAE;QAC7C,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QACrB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,UAAU,CAAC,EAAE,IAAI,CAAC;QAClB,QAAQ,CAAC,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC;QAChC,OAAO,CAAC,EAAE,OAAO,CAAC;KACrB,GAAG,IAAI,CAAC;IACT,MAAM,IAAI,IAAI,CAAC;CAClB,CAAC"}