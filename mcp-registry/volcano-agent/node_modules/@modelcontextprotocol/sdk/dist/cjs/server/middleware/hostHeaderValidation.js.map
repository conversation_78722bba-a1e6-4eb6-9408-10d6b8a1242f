{"version": 3, "file": "hostHeaderValidation.js", "sourceRoot": "", "sources": ["../../../../src/server/middleware/hostHeaderValidation.ts"], "names": [], "mappings": ";;AAqBA,oDA4CC;AAWD,0DAEC;AA5ED;;;;;;;;;;;;;;;;;;GAkBG;AACH,SAAgB,oBAAoB,CAAC,gBAA0B;IAC3D,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACvD,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;QACpC,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACjB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACH,IAAI,EAAE,CAAC,KAAK;oBACZ,OAAO,EAAE,qBAAqB;iBACjC;gBACD,EAAE,EAAE,IAAI;aACX,CAAC,CAAC;YACH,OAAO;QACX,CAAC;QAED,4EAA4E;QAC5E,IAAI,QAAgB,CAAC;QACrB,IAAI,CAAC;YACD,QAAQ,GAAG,IAAI,GAAG,CAAC,UAAU,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC;QACxD,CAAC;QAAC,MAAM,CAAC;YACL,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACjB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACH,IAAI,EAAE,CAAC,KAAK;oBACZ,OAAO,EAAE,wBAAwB,UAAU,EAAE;iBAChD;gBACD,EAAE,EAAE,IAAI;aACX,CAAC,CAAC;YACH,OAAO;QACX,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACjB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACH,IAAI,EAAE,CAAC,KAAK;oBACZ,OAAO,EAAE,iBAAiB,QAAQ,EAAE;iBACvC;gBACD,EAAE,EAAE,IAAI;aACX,CAAC,CAAC;YACH,OAAO;QACX,CAAC;QACD,IAAI,EAAE,CAAC;IACX,CAAC,CAAC;AACN,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,uBAAuB;IACnC,OAAO,oBAAoB,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;AACrE,CAAC"}