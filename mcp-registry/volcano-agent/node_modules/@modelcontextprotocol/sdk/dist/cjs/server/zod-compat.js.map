{"version": 3, "file": "zod-compat.js", "sourceRoot": "", "sources": ["../../../src/server/zod-compat.ts"], "names": [], "mappings": ";AAAA,gBAAgB;AAChB,uDAAuD;AACvD,yDAAyD;AACzD,uDAAuD;;;;;;;;;;;;;;;;;;;;;;;;;AAqDvD,gCAIC;AAGD,0CAWC;AAGD,8BAYC;AAED,wCAYC;AAGD,wCAyBC;AAQD,sDAiDC;AAOD,oDAoBC;AAUD,oDAEC;AAMD,4CAWC;AAOD,0CAwBC;AA3QD,6CAA+B;AAC/B,oDAAsC;AA8CtC,4BAA4B;AAC5B,SAAgB,UAAU,CAAC,CAAY;IACnC,6DAA6D;IAC7D,MAAM,MAAM,GAAG,CAA6B,CAAC;IAC7C,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;AACzB,CAAC;AAED,8BAA8B;AAC9B,SAAgB,eAAe,CAAC,KAAwB;IACpD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACpC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,qBAAqB;IAExE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACvC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAEhD,IAAI,KAAK;QAAE,OAAO,MAAM,CAAC,MAAM,CAAC,KAAoC,CAAC,CAAC;IACtE,IAAI,KAAK;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAsC,CAAC,CAAC;IAEtE,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;AACpE,CAAC;AAED,0BAA0B;AAC1B,SAAgB,SAAS,CACrB,MAAS,EACT,IAAa;IAEb,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;QACrB,mCAAmC;QACnC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9C,OAAO,MAAuF,CAAC;IACnG,CAAC;IACD,MAAM,QAAQ,GAAG,MAAuB,CAAC;IACzC,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACxC,OAAO,MAAuF,CAAC;AACnG,CAAC;AAEM,KAAK,UAAU,cAAc,CAChC,MAAS,EACT,IAAa;IAEb,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;QACrB,wCAAwC;QACxC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACzD,OAAO,MAAuF,CAAC;IACnG,CAAC;IACD,MAAM,QAAQ,GAAG,MAAuB,CAAC;IACzC,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACnD,OAAO,MAAuF,CAAC;AACnG,CAAC;AAED,2BAA2B;AAC3B,SAAgB,cAAc,CAAC,MAAmC;IAC9D,IAAI,CAAC,MAAM;QAAE,OAAO,SAAS,CAAC;IAE9B,sEAAsE;IACtE,IAAI,QAAmF,CAAC;IAExF,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;QACrB,MAAM,QAAQ,GAAG,MAAkC,CAAC;QACpD,QAAQ,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC;IACzC,CAAC;SAAM,CAAC;QACJ,MAAM,QAAQ,GAAG,MAAkC,CAAC;QACpD,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED,IAAI,CAAC,QAAQ;QAAE,OAAO,SAAS,CAAC;IAEhC,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC;YACD,OAAO,QAAQ,EAAE,CAAC;QACtB,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED,+BAA+B;AAC/B;;;;GAIG;AACH,SAAgB,qBAAqB,CAAC,MAAiD;IACnF,IAAI,CAAC,MAAM;QAAE,OAAO,SAAS,CAAC;IAE9B,8DAA8D;IAC9D,8EAA8E;IAC9E,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC7B,qEAAqE;QACrE,0DAA0D;QAC1D,MAAM,IAAI,GAAG,MAAkC,CAAC;QAChD,MAAM,IAAI,GAAG,MAAkC,CAAC;QAEhD,2EAA2E;QAC3E,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAC3B,0EAA0E;YAC1E,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACrC,IACI,MAAM,CAAC,MAAM,GAAG,CAAC;gBACjB,MAAM,CAAC,KAAK,CACR,CAAC,CAAC,EAAE,CACA,OAAO,CAAC,KAAK,QAAQ;oBACrB,CAAC,KAAK,IAAI;oBACV,CAAE,CAA8B,CAAC,IAAI,KAAK,SAAS;wBAC9C,CAA8B,CAAC,IAAI,KAAK,SAAS;wBAClD,OAAQ,CAAyB,CAAC,KAAK,KAAK,UAAU,CAAC,CAClE,EACH,CAAC;gBACC,OAAO,eAAe,CAAC,MAA2B,CAAC,CAAC;YACxD,CAAC;QACL,CAAC;IACL,CAAC;IAED,8DAA8D;IAC9D,yCAAyC;IACzC,IAAI,UAAU,CAAC,MAAmB,CAAC,EAAE,CAAC;QAClC,4BAA4B;QAC5B,MAAM,QAAQ,GAAG,MAAkC,CAAC;QACpD,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC;QAC/B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,EAAE,CAAC;YAC5D,OAAO,MAAyB,CAAC;QACrC,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,4BAA4B;QAC5B,MAAM,QAAQ,GAAG,MAAkC,CAAC;QACpD,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC/B,OAAO,MAAyB,CAAC;QACrC,CAAC;IACL,CAAC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC;AAED,mCAAmC;AACnC;;;GAGG;AACH,SAAgB,oBAAoB,CAAC,KAAc;IAC/C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QACrC,8BAA8B;QAC9B,IAAI,SAAS,IAAI,KAAK,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC1D,OAAO,KAAK,CAAC,OAAO,CAAC;QACzB,CAAC;QACD,IAAI,QAAQ,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9E,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,SAAS,IAAI,UAAU,EAAE,CAAC;gBAC1E,OAAO,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;QACD,uCAAuC;QACvC,IAAI,CAAC;YACD,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;IACL,CAAC;IACD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACzB,CAAC;AAED,iCAAiC;AACjC;;;;;;GAMG;AACH,SAAgB,oBAAoB,CAAC,MAAiB;IAClD,OAAQ,MAAmC,CAAC,WAAW,CAAC;AAC5D,CAAC;AAED;;;GAGG;AACH,SAAgB,gBAAgB,CAAC,MAAiB;IAC9C,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;QACrB,MAAM,QAAQ,GAAG,MAAkC,CAAC;QACpD,OAAO,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,KAAK,UAAU,CAAC;IACnD,CAAC;IACD,MAAM,QAAQ,GAAG,MAAkC,CAAC;IACpD,6BAA6B;IAC7B,IAAI,OAAQ,MAAyC,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;QAC9E,OAAQ,MAAwC,CAAC,UAAU,EAAE,CAAC;IAClE,CAAC;IACD,OAAO,QAAQ,CAAC,IAAI,EAAE,QAAQ,KAAK,aAAa,CAAC;AACrD,CAAC;AAED;;;;GAIG;AACH,SAAgB,eAAe,CAAC,MAAiB;IAC7C,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;QACrB,MAAM,QAAQ,GAAG,MAAkC,CAAC;QACpD,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC;QAC/B,IAAI,GAAG,EAAE,CAAC;YACN,4CAA4C;YAC5C,IAAI,GAAG,CAAC,KAAK,KAAK,SAAS;gBAAE,OAAO,GAAG,CAAC,KAAK,CAAC;YAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrD,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC;QACL,CAAC;IACL,CAAC;IACD,MAAM,QAAQ,GAAG,MAAkC,CAAC;IACpD,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC;IAC1B,IAAI,GAAG,EAAE,CAAC;QACN,IAAI,GAAG,CAAC,KAAK,KAAK,SAAS;YAAE,OAAO,GAAG,CAAC,KAAK,CAAC;QAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrD,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC;IACL,CAAC;IACD,gEAAgE;IAChE,MAAM,WAAW,GAAI,MAA8B,CAAC,KAAK,CAAC;IAC1D,IAAI,WAAW,KAAK,SAAS;QAAE,OAAO,WAAW,CAAC;IAClD,OAAO,SAAS,CAAC;AACrB,CAAC"}