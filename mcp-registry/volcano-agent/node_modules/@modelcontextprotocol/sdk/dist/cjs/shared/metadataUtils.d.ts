import { BaseMetadata } from '../types.js';
/**
 * Utilities for working with BaseMetadata objects.
 */
/**
 * Gets the display name for an object with BaseMetadata.
 * For tools, the precedence is: title → annotations.title → name
 * For other objects: title → name
 * This implements the spec requirement: "if no title is provided, name should be used for display purposes"
 */
export declare function getDisplayName(metadata: BaseMetadata | (BaseMetadata & {
    annotations?: {
        title?: string;
    };
})): string;
//# sourceMappingURL=metadataUtils.d.ts.map