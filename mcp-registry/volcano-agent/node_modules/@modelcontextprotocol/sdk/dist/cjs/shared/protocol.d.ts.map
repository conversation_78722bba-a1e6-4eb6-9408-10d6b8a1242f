{"version": 3, "file": "protocol.d.ts", "sourceRoot": "", "sources": ["../../../src/shared/protocol.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,YAAY,EAAa,MAAM,yBAAyB,CAAC;AAC9F,OAAO,EAEH,kBAAkB,EAGlB,cAAc,EAGd,qBAAqB,EAGrB,qBAAqB,EAErB,sBAAsB,EAOtB,cAAc,EAId,QAAQ,EAIR,SAAS,EACT,MAAM,EACN,kBAAkB,EAClB,WAAW,EAEX,WAAW,EACX,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EAEnB,IAAI,EAGJ,OAAO,EACP,YAAY,EAGf,MAAM,aAAa,CAAC;AACrB,OAAO,EAAE,SAAS,EAAE,oBAAoB,EAAE,MAAM,gBAAgB,CAAC;AACjE,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAc,SAAS,EAAE,gBAAgB,EAAiB,iBAAiB,EAAE,MAAM,qCAAqC,CAAC;AAEhI,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAEvD;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,CAAC,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;AAE5D;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG;IAC1B;;;;;;OAMG;IACH,yBAAyB,CAAC,EAAE,OAAO,CAAC;IACpC;;;;;OAKG;IACH,4BAA4B,CAAC,EAAE,MAAM,EAAE,CAAC;IACxC;;;OAGG;IACH,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB;;;OAGG;IACH,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IACpC;;;OAGG;IACH,uBAAuB,CAAC,EAAE,MAAM,CAAC;IACjC;;;;;;OAMG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;CAC7B,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,4BAA4B,QAAQ,CAAC;AAElD;;GAEG;AACH,MAAM,MAAM,cAAc,GAAG;IACzB;;;;OAIG;IACH,UAAU,CAAC,EAAE,gBAAgB,CAAC;IAE9B;;OAEG;IACH,MAAM,CAAC,EAAE,WAAW,CAAC;IAErB;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;;;OAIG;IACH,sBAAsB,CAAC,EAAE,OAAO,CAAC;IAEjC;;;;OAIG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB;;OAEG;IACH,IAAI,CAAC,EAAE,kBAAkB,CAAC;IAE1B;;OAEG;IACH,WAAW,CAAC,EAAE,mBAAmB,CAAC;CACrC,GAAG,oBAAoB,CAAC;AAEzB;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG;IAC9B;;OAEG;IACH,gBAAgB,CAAC,EAAE,SAAS,CAAC;IAE7B;;OAEG;IACH,WAAW,CAAC,EAAE,mBAAmB,CAAC;CACrC,CAAC;AAEF;;GAEG;AAEH,MAAM,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;AAErE;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAC7B;;;;;;OAMG;IACH,UAAU,CAAC,UAAU,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEzD;;;;;;OAMG;IACH,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEvC;;;;;;OAMG;IACH,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,GAAG,QAAQ,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAE/F;;;;;OAKG;IACH,aAAa,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAE/C;;;;;;OAMG;IACH,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEhG;;;;;OAKG;IACH,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,KAAK,EAAE,IAAI,EAAE,CAAC;QAAC,UAAU,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;CAC/E;AAED;;GAEG;AACH,MAAM,MAAM,mBAAmB,CAAC,YAAY,SAAS,OAAO,EAAE,iBAAiB,SAAS,YAAY,IAAI;IACpG;;OAEG;IACH,MAAM,EAAE,WAAW,CAAC;IAEpB;;OAEG;IACH,QAAQ,CAAC,EAAE,QAAQ,CAAC;IAEpB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,KAAK,CAAC,EAAE,WAAW,CAAC;IAEpB;;;OAGG;IACH,SAAS,EAAE,SAAS,CAAC;IAErB,MAAM,CAAC,EAAE,MAAM,CAAC;IAEhB,SAAS,CAAC,EAAE,gBAAgB,CAAC;IAE7B,gBAAgB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEjC;;OAEG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAE1B;;;;OAIG;IACH,gBAAgB,EAAE,CAAC,YAAY,EAAE,iBAAiB,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAErE;;;;OAIG;IACH,WAAW,EAAE,CAAC,CAAC,SAAS,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,kBAAkB,KAAK,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IAErI;;;;OAIG;IACH,cAAc,CAAC,EAAE,MAAM,IAAI,CAAC;IAE5B;;;;OAIG;IACH,wBAAwB,CAAC,EAAE,MAAM,IAAI,CAAC;CACzC,CAAC;AAcF;;;GAGG;AACH,8BAAsB,QAAQ,CAAC,YAAY,SAAS,OAAO,EAAE,iBAAiB,SAAS,YAAY,EAAE,WAAW,SAAS,MAAM;IA8C/G,OAAO,CAAC,QAAQ,CAAC;IA7C7B,OAAO,CAAC,UAAU,CAAC,CAAY;IAC/B,OAAO,CAAC,iBAAiB,CAAK;IAC9B,OAAO,CAAC,gBAAgB,CAGV;IACd,OAAO,CAAC,+BAA+B,CAA8C;IACrF,OAAO,CAAC,qBAAqB,CAAgF;IAC7G,OAAO,CAAC,iBAAiB,CAA6E;IACtG,OAAO,CAAC,iBAAiB,CAA4C;IACrE,OAAO,CAAC,YAAY,CAAuC;IAC3D,OAAO,CAAC,8BAA8B,CAAqB;IAG3D,OAAO,CAAC,mBAAmB,CAAkC;IAE7D,OAAO,CAAC,UAAU,CAAC,CAAY;IAC/B,OAAO,CAAC,iBAAiB,CAAC,CAAmB;IAE7C,OAAO,CAAC,iBAAiB,CAAgF;IAEzG;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,IAAI,CAAC;IAErB;;;;OAIG;IACH,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IAEjC;;OAEG;IACH,sBAAsB,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,mBAAmB,CAAC,YAAY,EAAE,iBAAiB,CAAC,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IAExI;;OAEG;IACH,2BAA2B,CAAC,EAAE,CAAC,YAAY,EAAE,YAAY,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;gBAExD,QAAQ,CAAC,EAAE,eAAe,YAAA;YAwLhC,SAAS;IASvB,OAAO,CAAC,aAAa;IAiBrB,OAAO,CAAC,aAAa;IAkBrB,OAAO,CAAC,eAAe;IAQvB;;;;OAIG;IACG,OAAO,CAAC,SAAS,EAAE,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;IA+BlD,OAAO,CAAC,QAAQ;IAiBhB,OAAO,CAAC,QAAQ;IAIhB,OAAO,CAAC,eAAe;IAcvB,OAAO,CAAC,UAAU;IA8JlB,OAAO,CAAC,WAAW;IA6BnB,OAAO,CAAC,WAAW;IAkDnB,IAAI,SAAS,IAAI,SAAS,GAAG,SAAS,CAErC;IAED;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAI5B;;;;OAIG;IACH,SAAS,CAAC,QAAQ,CAAC,yBAAyB,CAAC,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,GAAG,IAAI;IAElF;;;;OAIG;IACH,SAAS,CAAC,QAAQ,CAAC,4BAA4B,CAAC,MAAM,EAAE,iBAAiB,CAAC,QAAQ,CAAC,GAAG,IAAI;IAE1F;;;;OAIG;IACH,SAAS,CAAC,QAAQ,CAAC,8BAA8B,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAEvE;;;;OAIG;IACH,SAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAE7D;;;;OAIG;IACH,SAAS,CAAC,QAAQ,CAAC,2BAA2B,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAEpE;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;cACc,aAAa,CAAC,CAAC,SAAS,SAAS,EAC9C,OAAO,EAAE,YAAY,EACrB,YAAY,EAAE,CAAC,EACf,OAAO,CAAC,EAAE,cAAc,GACzB,cAAc,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;IAiF/D;;;;OAIG;IACH,OAAO,CAAC,CAAC,SAAS,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IA8JxH;;;;OAIG;cACa,OAAO,CAAC,MAAM,EAAE,cAAc,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC,aAAa,CAAC;IAK3G;;;;OAIG;cACa,aAAa,CAAC,CAAC,SAAS,SAAS,EAC7C,MAAM,EAAE,qBAAqB,CAAC,QAAQ,CAAC,EACvC,YAAY,EAAE,CAAC,EACf,OAAO,CAAC,EAAE,cAAc,GACzB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAK3B;;;;OAIG;cACa,SAAS,CAAC,MAAM,CAAC,EAAE;QAAE,MAAM,CAAC,EAAE,MAAM,CAAA;KAAE,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,qBAAqB,CAAC,CAAC;IAKtI;;;;OAIG;cACa,UAAU,CAAC,MAAM,EAAE;QAAE,MAAM,EAAE,MAAM,CAAA;KAAE,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,sBAAsB,CAAC,CAAC;IAKtI;;OAEG;IACG,YAAY,CAAC,YAAY,EAAE,iBAAiB,EAAE,OAAO,CAAC,EAAE,mBAAmB,GAAG,OAAO,CAAC,IAAI,CAAC;IA8GjG;;;;OAIG;IACH,iBAAiB,CAAC,CAAC,SAAS,eAAe,EACvC,aAAa,EAAE,CAAC,EAChB,OAAO,EAAE,CACL,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,EACxB,KAAK,EAAE,mBAAmB,CAAC,YAAY,EAAE,iBAAiB,CAAC,KAC1D,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GACxC,IAAI;IAUP;;OAEG;IACH,oBAAoB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAI1C;;OAEG;IACH,0BAA0B,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAMhD;;;;OAIG;IACH,sBAAsB,CAAC,CAAC,SAAS,eAAe,EAC5C,kBAAkB,EAAE,CAAC,EACrB,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,GACjE,IAAI;IAQP;;OAEG;IACH,yBAAyB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAI/C;;;OAGG;IACH,OAAO,CAAC,2BAA2B;IAQnC;;;;;;;;;;OAUG;YACW,mBAAmB;IAUjC;;;;OAIG;YACW,eAAe;IAqB7B;;;;;;OAMG;YACW,kBAAkB;IAiChC,OAAO,CAAC,gBAAgB;CAwF3B;AAMD,wBAAgB,iBAAiB,CAAC,IAAI,EAAE,kBAAkB,EAAE,UAAU,EAAE,OAAO,CAAC,kBAAkB,CAAC,GAAG,kBAAkB,CAAC;AACzH,wBAAgB,iBAAiB,CAAC,IAAI,EAAE,kBAAkB,EAAE,UAAU,EAAE,OAAO,CAAC,kBAAkB,CAAC,GAAG,kBAAkB,CAAC"}