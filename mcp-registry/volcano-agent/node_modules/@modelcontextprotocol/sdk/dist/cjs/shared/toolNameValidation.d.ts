/**
 * Tool name validation utilities according to SEP: Specify Format for Tool Names
 *
 * Tool names SHOULD be between 1 and 128 characters in length (inclusive).
 * Tool names are case-sensitive.
 * Allowed characters: uppercase and lowercase ASCII letters (A-Z, a-z), digits
 * (0-9), underscore (_), dash (-), and dot (.).
 * Tool names SHOULD NOT contain spaces, commas, or other special characters.
 */
/**
 * Validates a tool name according to the SEP specification
 * @param name - The tool name to validate
 * @returns An object containing validation result and any warnings
 */
export declare function validateToolName(name: string): {
    isValid: boolean;
    warnings: string[];
};
/**
 * Issues warnings for non-conforming tool names
 * @param name - The tool name that triggered the warnings
 * @param warnings - Array of warning messages
 */
export declare function issueToolNameWarning(name: string, warnings: string[]): void;
/**
 * Validates a tool name and issues warnings for non-conforming names
 * @param name - The tool name to validate
 * @returns true if the name is valid, false otherwise
 */
export declare function validateAndWarnToolName(name: string): boolean;
//# sourceMappingURL=toolNameValidation.d.ts.map