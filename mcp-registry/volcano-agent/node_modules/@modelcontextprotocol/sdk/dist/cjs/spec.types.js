"use strict";
/**
 * This file is automatically generated from the Model Context Protocol specification.
 *
 * Source: https://github.com/modelcontextprotocol/modelcontextprotocol
 * Pulled from: https://raw.githubusercontent.com/modelcontextprotocol/modelcontextprotocol/main/schema/draft/schema.ts
 * Last updated from commit: 35fa160caf287a9c48696e3ae452c0645c713669
 *
 * DO NOT EDIT THIS FILE MANUALLY. Changes will be overwritten by automated updates.
 * To update this file, run: npm run fetch:spec-types
 */ /* JSON-RPC types */
Object.defineProperty(exports, "__esModule", { value: true });
exports.URL_ELICITATION_REQUIRED = exports.INTERNAL_ERROR = exports.INVALID_PARAMS = exports.METHOD_NOT_FOUND = exports.INVALID_REQUEST = exports.PARSE_ERROR = exports.JSONRPC_VERSION = exports.LATEST_PROTOCOL_VERSION = void 0;
/** @internal */
exports.LATEST_PROTOCOL_VERSION = "DRAFT-2026-v1";
/** @internal */
exports.JSONRPC_VERSION = "2.0";
// Standard JSON-RPC error codes
exports.PARSE_ERROR = -32700;
exports.INVALID_REQUEST = -32600;
exports.METHOD_NOT_FOUND = -32601;
exports.INVALID_PARAMS = -32602;
exports.INTERNAL_ERROR = -32603;
// Implementation-specific JSON-RPC error codes [-32000, -32099]
/** @internal */
exports.URL_ELICITATION_REQUIRED = -32042;
//# sourceMappingURL=spec.types.js.map