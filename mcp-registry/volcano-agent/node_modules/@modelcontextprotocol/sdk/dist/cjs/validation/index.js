"use strict";
/**
 * JSON Schema validation
 *
 * This module provides configurable JSON Schema validation for the MCP SDK.
 * Choose a validator based on your runtime environment:
 *
 * - AjvJsonSchemaValidator: Best for Node.js (default, fastest)
 *   Import from: @modelcontextprotocol/sdk/validation/ajv
 *   Requires peer dependencies: ajv, ajv-formats
 *
 * - CfWorkerJsonSchemaValidator: Best for edge runtimes
 *   Import from: @modelcontextprotocol/sdk/validation/cfworker
 *   Requires peer dependency: @cfworker/json-schema
 *
 * @example
 * ```typescript
 * // For Node.js with AJV
 * import { AjvJsonSchemaValidator } from '@modelcontextprotocol/sdk/validation/ajv';
 * const validator = new AjvJsonSchemaValidator();
 *
 * // For Cloudflare Workers
 * import { CfWorkerJsonSchemaValidator } from '@modelcontextprotocol/sdk/validation/cfworker';
 * const validator = new CfWorkerJsonSchemaValidator();
 * ```
 *
 * @module validation
 */
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=index.js.map