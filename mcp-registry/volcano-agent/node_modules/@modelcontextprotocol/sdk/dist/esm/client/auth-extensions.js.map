{"version": 3, "file": "auth-extensions.js", "sourceRoot": "", "sources": ["../../../src/client/auth-extensions.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAMH;;;;;;GAMG;AACH,MAAM,UAAU,uBAAuB,CAAC,OAQvC;IACG,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE;QAC7C,oDAAoD;QACpD,IAAI,OAAO,UAAU,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YAC3C,MAAM,IAAI,SAAS,CACf,qNAAqN,CACxN,CAAC;QACN,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC;QAElC,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,IAAI,QAAQ,EAAE,MAAM,IAAI,GAAG,CAAC,CAAC;QACrE,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,GAAG,CAAC;QAEvD,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC1C,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;QAEnE,MAAM,UAAU,GAAG;YACf,GAAG,EAAE,OAAO,CAAC,MAAM;YACnB,GAAG,EAAE,OAAO,CAAC,OAAO;YACpB,GAAG,EAAE,QAAQ;YACb,GAAG,EAAE,GAAG,GAAG,eAAe;YAC1B,GAAG,EAAE,GAAG;YACR,GAAG;SACN,CAAC;QACF,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,UAAU,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;QAElF,yCAAyC;QACzC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACxB,IAAI,GAAY,CAAC;QACjB,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACzC,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvE,GAAG,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC1D,CAAC;iBAAM,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,GAAG,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,EAAE,CAAC,CAAC;YACpD,CAAC;QACL,CAAC;aAAM,IAAI,OAAO,CAAC,UAAU,YAAY,UAAU,EAAE,CAAC;YAClD,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC;YAC7B,CAAC;iBAAM,CAAC;gBACJ,4DAA4D;gBAC5D,GAAG,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC;YACpF,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,eAAe;YACf,GAAG,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAiB,EAAE,GAAG,CAAC,CAAC;QAC/D,CAAC;QAED,WAAW;QACX,MAAM,SAAS,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;aAC3C,kBAAkB,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;aACvC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;aACzB,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC;aAC3B,WAAW,CAAC,QAAQ,CAAC;aACrB,WAAW,CAAC,GAAG,CAAC;aAChB,iBAAiB,CAAC,GAAG,GAAG,eAAe,CAAC;aACxC,MAAM,CAAC,GAAG,CAAC;aACX,IAAI,CAAC,GAAwC,CAAC,CAAC;QAEpD,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,CAAC,uBAAuB,EAAE,wDAAwD,CAAC,CAAC;IAClG,CAAC,CAAC;AACN,CAAC;AAsBD;;;;;;;;;;;;;;;GAeG;AACH,MAAM,OAAO,yBAAyB;IAKlC,YAAY,OAAyC;QACjD,IAAI,CAAC,WAAW,GAAG;YACf,SAAS,EAAE,OAAO,CAAC,QAAQ;YAC3B,aAAa,EAAE,OAAO,CAAC,YAAY;SACtC,CAAC;QACF,IAAI,CAAC,eAAe,GAAG;YACnB,WAAW,EAAE,OAAO,CAAC,UAAU,IAAI,2BAA2B;YAC9D,aAAa,EAAE,EAAE;YACjB,WAAW,EAAE,CAAC,oBAAoB,CAAC;YACnC,0BAA0B,EAAE,qBAAqB;SACpD,CAAC;IACN,CAAC;IAED,IAAI,WAAW;QACX,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,IAAI,cAAc;QACd,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,iBAAiB;QACb,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,qBAAqB,CAAC,IAA4B;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,MAAM;QACF,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,UAAU,CAAC,MAAmB;QAC1B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,uBAAuB;QACnB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;IACvF,CAAC;IAED,gBAAgB;QACZ,kCAAkC;IACtC,CAAC;IAED,YAAY;QACR,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC5E,CAAC;IAED,mBAAmB,CAAC,KAAc;QAC9B,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,EAAE,UAAU,EAAE,oBAAoB,EAAE,CAAC,CAAC;QACzE,IAAI,KAAK;YAAE,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AAiCD;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,OAAO,qBAAqB;IAM9B,YAAY,OAAqC;QAC7C,IAAI,CAAC,WAAW,GAAG;YACf,SAAS,EAAE,OAAO,CAAC,QAAQ;SAC9B,CAAC;QACF,IAAI,CAAC,eAAe,GAAG;YACnB,WAAW,EAAE,OAAO,CAAC,UAAU,IAAI,wBAAwB;YAC3D,aAAa,EAAE,EAAE;YACjB,WAAW,EAAE,CAAC,oBAAoB,CAAC;YACnC,0BAA0B,EAAE,iBAAiB;SAChD,CAAC;QACF,IAAI,CAAC,uBAAuB,GAAG,uBAAuB,CAAC;YACnD,MAAM,EAAE,OAAO,CAAC,QAAQ;YACxB,OAAO,EAAE,OAAO,CAAC,QAAQ;YACzB,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,GAAG,EAAE,OAAO,CAAC,SAAS;YACtB,eAAe,EAAE,OAAO,CAAC,kBAAkB;SAC9C,CAAC,CAAC;IACP,CAAC;IAED,IAAI,WAAW;QACX,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,IAAI,cAAc;QACd,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,iBAAiB;QACb,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,qBAAqB,CAAC,IAA4B;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,MAAM;QACF,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,UAAU,CAAC,MAAmB;QAC1B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,uBAAuB;QACnB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;IACvF,CAAC;IAED,gBAAgB;QACZ,kCAAkC;IACtC,CAAC;IAED,YAAY;QACR,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC5E,CAAC;IAED,mBAAmB,CAAC,KAAc;QAC9B,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,EAAE,UAAU,EAAE,oBAAoB,EAAE,CAAC,CAAC;QACzE,IAAI,KAAK;YAAE,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AAyBD;;;;;;GAMG;AACH,MAAM,OAAO,2BAA2B;IAMpC,YAAY,OAA2C;QACnD,IAAI,CAAC,WAAW,GAAG;YACf,SAAS,EAAE,OAAO,CAAC,QAAQ;SAC9B,CAAC;QACF,IAAI,CAAC,eAAe,GAAG;YACnB,WAAW,EAAE,OAAO,CAAC,UAAU,IAAI,+BAA+B;YAClE,aAAa,EAAE,EAAE;YACjB,WAAW,EAAE,CAAC,oBAAoB,CAAC;YACnC,0BAA0B,EAAE,iBAAiB;SAChD,CAAC;QAEF,MAAM,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAAC;QAC7C,IAAI,CAAC,uBAAuB,GAAG,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE;YACtD,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;YAC1C,MAAM,CAAC,GAAG,CAAC,uBAAuB,EAAE,wDAAwD,CAAC,CAAC;QAClG,CAAC,CAAC;IACN,CAAC;IAED,IAAI,WAAW;QACX,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,IAAI,cAAc;QACd,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,iBAAiB;QACb,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,qBAAqB,CAAC,IAA4B;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,MAAM;QACF,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,UAAU,CAAC,MAAmB;QAC1B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,uBAAuB;QACnB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;IACvF,CAAC;IAED,gBAAgB;QACZ,kCAAkC;IACtC,CAAC;IAED,YAAY;QACR,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC5E,CAAC;IAED,mBAAmB,CAAC,KAAc;QAC9B,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,EAAE,UAAU,EAAE,oBAAoB,EAAE,CAAC,CAAC;QACzE,IAAI,KAAK;YAAE,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ"}