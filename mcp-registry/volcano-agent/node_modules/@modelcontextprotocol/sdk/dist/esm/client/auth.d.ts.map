{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../../src/client/auth.ts"], "names": [], "mappings": "AAEA,OAAO,EACH,mBAAmB,EAEnB,2BAA2B,EAC3B,WAAW,EACX,aAAa,EACb,0BAA0B,EAC1B,8BAA8B,EAE9B,2BAA2B,EAE9B,MAAM,mBAAmB,CAAC;AAQ3B,OAAO,EAKH,UAAU,EAGb,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AAEnD;;GAEG;AACH,MAAM,MAAM,uBAAuB,GAAG,CAClC,OAAO,EAAE,OAAO,EAChB,MAAM,EAAE,eAAe,EACvB,GAAG,EAAE,MAAM,GAAG,GAAG,EACjB,QAAQ,CAAC,EAAE,2BAA2B,KACrC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAE1B;;;;;;GAMG;AACH,MAAM,WAAW,mBAAmB;IAChC;;;;OAIG;IACH,IAAI,WAAW,IAAI,MAAM,GAAG,GAAG,GAAG,SAAS,CAAC;IAE5C;;OAEG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAE3B;;OAEG;IACH,IAAI,cAAc,IAAI,mBAAmB,CAAC;IAE1C;;OAEG;IACH,KAAK,CAAC,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAEnC;;;;OAIG;IACH,iBAAiB,IAAI,2BAA2B,GAAG,SAAS,GAAG,OAAO,CAAC,2BAA2B,GAAG,SAAS,CAAC,CAAC;IAEhH;;;;;;;OAOG;IACH,qBAAqB,CAAC,CAAC,iBAAiB,EAAE,2BAA2B,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAE7F;;;OAGG;IACH,MAAM,IAAI,WAAW,GAAG,SAAS,GAAG,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC;IAErE;;;OAGG;IACH,UAAU,CAAC,MAAM,EAAE,WAAW,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEtD;;OAEG;IACH,uBAAuB,CAAC,gBAAgB,EAAE,GAAG,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAErE;;;OAGG;IACH,gBAAgB,CAAC,YAAY,EAAE,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAE7D;;;OAGG;IACH,YAAY,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAEzC;;;;;;;;;;;;;;;;;OAiBG;IACH,uBAAuB,CAAC,EAAE,uBAAuB,CAAC;IAElD;;;;;;OAMG;IACH,mBAAmB,CAAC,CAAC,SAAS,EAAE,MAAM,GAAG,GAAG,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC;IAE3F;;;;OAIG;IACH,qBAAqB,CAAC,CAAC,KAAK,EAAE,KAAK,GAAG,QAAQ,GAAG,QAAQ,GAAG,UAAU,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACH,mBAAmB,CAAC,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,eAAe,GAAG,OAAO,CAAC,eAAe,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC;CAC5G;AAED,MAAM,MAAM,UAAU,GAAG,YAAY,GAAG,UAAU,CAAC;AAEnD,qBAAa,iBAAkB,SAAQ,KAAK;gBAC5B,OAAO,CAAC,EAAE,MAAM;CAG/B;AAED,KAAK,gBAAgB,GAAG,qBAAqB,GAAG,oBAAoB,GAAG,MAAM,CAAC;AAS9E;;;;;;;;;;;GAWG;AACH,wBAAgB,sBAAsB,CAAC,iBAAiB,EAAE,2BAA2B,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,gBAAgB,CAiCnI;AAoED;;;;;;;;;;GAUG;AACH,wBAAsB,kBAAkB,CAAC,KAAK,EAAE,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CActF;AAED;;;;;GAKG;AACH,wBAAsB,IAAI,CACtB,QAAQ,EAAE,mBAAmB,EAC7B,OAAO,EAAE;IACL,SAAS,EAAE,MAAM,GAAG,GAAG,CAAC;IACxB,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,mBAAmB,CAAC,EAAE,GAAG,CAAC;IAC1B,OAAO,CAAC,EAAE,SAAS,CAAC;CACvB,GACF,OAAO,CAAC,UAAU,CAAC,CAgBrB;AAkJD;;;GAGG;AACH,wBAAgB,UAAU,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,CAQlD;AAED,wBAAsB,iBAAiB,CACnC,SAAS,EAAE,MAAM,GAAG,GAAG,EACvB,QAAQ,EAAE,mBAAmB,EAC7B,gBAAgB,CAAC,EAAE,8BAA8B,GAClD,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,CAmB1B;AAED;;GAEG;AACH,wBAAgB,4BAA4B,CAAC,GAAG,EAAE,QAAQ,GAAG;IAAE,mBAAmB,CAAC,EAAE,GAAG,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,CAAA;CAAE,CA8BzH;AA0BD;;;GAGG;AACH,wBAAgB,0BAA0B,CAAC,GAAG,EAAE,QAAQ,GAAG,GAAG,GAAG,SAAS,CAsBzE;AAED;;;;;GAKG;AACH,wBAAsB,sCAAsC,CACxD,SAAS,EAAE,MAAM,GAAG,GAAG,EACvB,IAAI,CAAC,EAAE;IAAE,eAAe,CAAC,EAAE,MAAM,CAAC;IAAC,mBAAmB,CAAC,EAAE,MAAM,GAAG,GAAG,CAAA;CAAE,EACvE,OAAO,GAAE,SAAiB,GAC3B,OAAO,CAAC,8BAA8B,CAAC,CAgBzC;AAwFD;;;;;;;GAOG;AACH,wBAAsB,qBAAqB,CACvC,MAAM,EAAE,MAAM,GAAG,GAAG,EACpB,EACI,sBAAsB,EACtB,eAAe,EAClB,GAAE;IACC,sBAAsB,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC;IACtC,eAAe,CAAC,EAAE,MAAM,CAAC;CACvB,EACN,OAAO,GAAE,SAAiB,GAC3B,OAAO,CAAC,aAAa,GAAG,SAAS,CAAC,CA4BpC;AAED;;;;;GAKG;AACH,wBAAgB,kBAAkB,CAAC,sBAAsB,EAAE,MAAM,GAAG,GAAG,GAAG;IAAE,GAAG,EAAE,GAAG,CAAC;IAAC,IAAI,EAAE,OAAO,GAAG,MAAM,CAAA;CAAE,EAAE,CAgD/G;AAED;;;;;;;;;;;;;;;GAeG;AACH,wBAAsB,mCAAmC,CACrD,sBAAsB,EAAE,MAAM,GAAG,GAAG,EACpC,EACI,OAAe,EACf,eAAyC,EAC5C,GAAE;IACC,OAAO,CAAC,EAAE,SAAS,CAAC;IACpB,eAAe,CAAC,EAAE,MAAM,CAAC;CACvB,GACP,OAAO,CAAC,2BAA2B,GAAG,SAAS,CAAC,CAyClD;AAED;;GAEG;AACH,wBAAsB,kBAAkB,CACpC,sBAAsB,EAAE,MAAM,GAAG,GAAG,EACpC,EACI,QAAQ,EACR,iBAAiB,EACjB,WAAW,EACX,KAAK,EACL,KAAK,EACL,QAAQ,EACX,EAAE;IACC,QAAQ,CAAC,EAAE,2BAA2B,CAAC;IACvC,iBAAiB,EAAE,2BAA2B,CAAC;IAC/C,WAAW,EAAE,MAAM,GAAG,GAAG,CAAC;IAC1B,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,GAAG,CAAC;CAClB,GACF,OAAO,CAAC;IAAE,gBAAgB,EAAE,GAAG,CAAC;IAAC,YAAY,EAAE,MAAM,CAAA;CAAE,CAAC,CAkD1D;AAED;;;;;;;;;;GAUG;AACH,wBAAgB,+BAA+B,CAC3C,iBAAiB,EAAE,MAAM,EACzB,YAAY,EAAE,MAAM,EACpB,WAAW,EAAE,MAAM,GAAG,GAAG,GAC1B,eAAe,CAOjB;AAwDD;;;;;;;;;;;GAWG;AACH,wBAAsB,qBAAqB,CACvC,sBAAsB,EAAE,MAAM,GAAG,GAAG,EACpC,EACI,QAAQ,EACR,iBAAiB,EACjB,iBAAiB,EACjB,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,uBAAuB,EACvB,OAAO,EACV,EAAE;IACC,QAAQ,CAAC,EAAE,2BAA2B,CAAC;IACvC,iBAAiB,EAAE,2BAA2B,CAAC;IAC/C,iBAAiB,EAAE,MAAM,CAAC;IAC1B,YAAY,EAAE,MAAM,CAAC;IACrB,WAAW,EAAE,MAAM,GAAG,GAAG,CAAC;IAC1B,QAAQ,CAAC,EAAE,GAAG,CAAC;IACf,uBAAuB,CAAC,EAAE,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;IACzE,OAAO,CAAC,EAAE,SAAS,CAAC;CACvB,GACF,OAAO,CAAC,WAAW,CAAC,CAWtB;AAED;;;;;;;;;;;GAWG;AACH,wBAAsB,oBAAoB,CACtC,sBAAsB,EAAE,MAAM,GAAG,GAAG,EACpC,EACI,QAAQ,EACR,iBAAiB,EACjB,YAAY,EACZ,QAAQ,EACR,uBAAuB,EACvB,OAAO,EACV,EAAE;IACC,QAAQ,CAAC,EAAE,2BAA2B,CAAC;IACvC,iBAAiB,EAAE,2BAA2B,CAAC;IAC/C,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,GAAG,CAAC;IACf,uBAAuB,CAAC,EAAE,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;IACzE,OAAO,CAAC,EAAE,SAAS,CAAC;CACvB,GACF,OAAO,CAAC,WAAW,CAAC,CAiBtB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,wBAAsB,UAAU,CAC5B,QAAQ,EAAE,mBAAmB,EAC7B,sBAAsB,EAAE,MAAM,GAAG,GAAG,EACpC,EACI,QAAQ,EACR,QAAQ,EACR,iBAAiB,EACjB,OAAO,EACV,GAAE;IACC,QAAQ,CAAC,EAAE,2BAA2B,CAAC;IACvC,QAAQ,CAAC,EAAE,GAAG,CAAC;IACf,uEAAuE;IACvE,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,OAAO,CAAC,EAAE,SAAS,CAAC;CAClB,GACP,OAAO,CAAC,WAAW,CAAC,CA+BtB;AAED;;GAEG;AACH,wBAAsB,cAAc,CAChC,sBAAsB,EAAE,MAAM,GAAG,GAAG,EACpC,EACI,QAAQ,EACR,cAAc,EACd,OAAO,EACV,EAAE;IACC,QAAQ,CAAC,EAAE,2BAA2B,CAAC;IACvC,cAAc,EAAE,mBAAmB,CAAC;IACpC,OAAO,CAAC,EAAE,SAAS,CAAC;CACvB,GACF,OAAO,CAAC,0BAA0B,CAAC,CA0BrC"}