{"version": 3, "file": "middleware.js", "sourceRoot": "", "sources": ["../../../src/client/middleware.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,4BAA4B,EAAuB,iBAAiB,EAAE,MAAM,WAAW,CAAC;AASvG;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAM,CAAC,MAAM,SAAS,GAClB,CAAC,QAA6B,EAAE,OAAsB,EAAc,EAAE,CACtE,IAAI,CAAC,EAAE;IACH,OAAO,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;QACzB,MAAM,WAAW,GAAG,KAAK,IAAuB,EAAE;YAC9C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAE3C,mDAAmD;YACnD,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,MAAM,EAAE,CAAC;YACvC,IAAI,MAAM,EAAE,CAAC;gBACT,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;YAClE,CAAC;YAED,OAAO,MAAM,IAAI,CAAC,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;QACnD,CAAC,CAAC;QAEF,IAAI,QAAQ,GAAG,MAAM,WAAW,EAAE,CAAC;QAEnC,uDAAuD;QACvD,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YAC1B,IAAI,CAAC;gBACD,MAAM,EAAE,mBAAmB,EAAE,KAAK,EAAE,GAAG,4BAA4B,CAAC,QAAQ,CAAC,CAAC;gBAE9E,mDAAmD;gBACnD,MAAM,SAAS,GAAG,OAAO,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAEhG,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE;oBAChC,SAAS;oBACT,mBAAmB;oBACnB,KAAK;oBACL,OAAO,EAAE,IAAI;iBAChB,CAAC,CAAC;gBAEH,IAAI,MAAM,KAAK,UAAU,EAAE,CAAC;oBACxB,MAAM,IAAI,iBAAiB,CAAC,iEAAiE,CAAC,CAAC;gBACnG,CAAC;gBAED,IAAI,MAAM,KAAK,YAAY,EAAE,CAAC;oBAC1B,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,MAAM,EAAE,CAAC,CAAC;gBAChF,CAAC;gBAED,sCAAsC;gBACtC,QAAQ,GAAG,MAAM,WAAW,EAAE,CAAC;YACnC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAI,KAAK,YAAY,iBAAiB,EAAE,CAAC;oBACrC,MAAM,KAAK,CAAC;gBAChB,CAAC;gBACD,MAAM,IAAI,iBAAiB,CAAC,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACxH,CAAC;QACL,CAAC;QAED,+DAA+D;QAC/D,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YAC1B,MAAM,GAAG,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACjE,MAAM,IAAI,iBAAiB,CAAC,6BAA6B,GAAG,EAAE,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC,CAAC;AACN,CAAC,CAAC;AA6CN;;;;;;;;;;;;;;;GAeG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,UAA0B,EAAE,EAAc,EAAE;IACpE,MAAM,EAAE,MAAM,EAAE,qBAAqB,GAAG,KAAK,EAAE,sBAAsB,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;IAE3G,MAAM,aAAa,GAAkB,KAAK,CAAC,EAAE;QACzC,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;QAEpG,IAAI,OAAO,GAAG,KAAK;YACf,CAAC,CAAC,QAAQ,MAAM,IAAI,GAAG,YAAY,KAAK,CAAC,OAAO,KAAK,QAAQ,KAAK;YAClE,CAAC,CAAC,QAAQ,MAAM,IAAI,GAAG,IAAI,MAAM,IAAI,UAAU,KAAK,QAAQ,KAAK,CAAC;QAEtE,sCAAsC;QACtC,IAAI,qBAAqB,IAAI,cAAc,EAAE,CAAC;YAC1C,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;iBAClD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,KAAK,EAAE,CAAC;iBACzC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChB,OAAO,IAAI,yBAAyB,UAAU,GAAG,CAAC;QACtD,CAAC;QAED,IAAI,sBAAsB,IAAI,eAAe,EAAE,CAAC;YAC5C,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;iBACnD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,KAAK,EAAE,CAAC;iBACzC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChB,OAAO,IAAI,0BAA0B,UAAU,GAAG,CAAC;QACvD,CAAC;QAED,IAAI,KAAK,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;YACzB,sCAAsC;YACtC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;aAAM,CAAC;YACJ,sCAAsC;YACtC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,KAAK,GAAG,MAAM,IAAI,aAAa,CAAC;IAEtC,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;QACjC,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QACpC,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,KAAK,CAAC;QACrC,MAAM,GAAG,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;QACjE,MAAM,cAAc,GAAG,qBAAqB,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAEtF,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACzC,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE/C,mDAAmD;YACnD,IAAI,QAAQ,CAAC,MAAM,IAAI,WAAW,EAAE,CAAC;gBACjC,KAAK,CAAC;oBACF,MAAM;oBACN,GAAG;oBACH,MAAM,EAAE,QAAQ,CAAC,MAAM;oBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,QAAQ;oBACR,cAAc;oBACd,eAAe,EAAE,sBAAsB,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;iBACzE,CAAC,CAAC;YACP,CAAC;YAED,OAAO,QAAQ,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE/C,4CAA4C;YAC5C,KAAK,CAAC;gBACF,MAAM;gBACN,GAAG;gBACH,MAAM,EAAE,CAAC;gBACT,UAAU,EAAE,eAAe;gBAC3B,QAAQ;gBACR,cAAc;gBACd,KAAK,EAAE,KAAc;aACxB,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC,CAAC;AACN,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,GAAG,UAAwB,EAAc,EAAE;IACxE,OAAO,IAAI,CAAC,EAAE;QACV,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;IACjE,CAAC,CAAC;AACN,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyDG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,OAAwF,EAAc,EAAE;IACrI,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,KAAqB,EAAE,IAAI,CAAC,CAAC;AAC/E,CAAC,CAAC"}