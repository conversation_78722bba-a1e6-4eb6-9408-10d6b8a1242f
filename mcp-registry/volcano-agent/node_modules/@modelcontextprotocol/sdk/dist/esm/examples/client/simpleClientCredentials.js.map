{"version": 3, "file": "simpleClientCredentials.js", "sourceRoot": "", "sources": ["../../../../src/examples/client/simpleClientCredentials.ts"], "names": [], "mappings": ";AAEA;;;;;;;;;;;;;;;;GAgBG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,6BAA6B,EAAE,MAAM,gCAAgC,CAAC;AAC/E,OAAO,EAAE,yBAAyB,EAAE,qBAAqB,EAAE,MAAM,iCAAiC,CAAC;AAGnG,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,2BAA2B,CAAC;AAErF,SAAS,cAAc;IACnB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;IAC3C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACZ,OAAO,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAChE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IAED,iEAAiE;IACjE,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;IAC7D,IAAI,aAAa,EAAE,CAAC;QAChB,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,OAAO,CAAC;QAC9D,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACpD,OAAO,IAAI,qBAAqB,CAAC;YAC7B,QAAQ;YACR,UAAU,EAAE,aAAa;YACzB,SAAS;SACZ,CAAC,CAAC;IACP,CAAC;IAED,oDAAoD;IACpD,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;IACnD,IAAI,CAAC,YAAY,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,kFAAkF,CAAC,CAAC;QAClG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IACxD,OAAO,IAAI,yBAAyB,CAAC;QACjC,QAAQ;QACR,YAAY;KACf,CAAC,CAAC;AACP,CAAC;AAED,KAAK,UAAU,IAAI;IACf,MAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;IAElC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,IAAI,EAAE,4BAA4B,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,CAAC;IAE1G,MAAM,SAAS,GAAG,IAAI,6BAA6B,CAAC,IAAI,GAAG,CAAC,kBAAkB,CAAC,EAAE;QAC7E,YAAY,EAAE,QAAQ;KACzB,CAAC,CAAC;IAEH,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAChC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IAEvC,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;IACvC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC;IAErF,MAAM,SAAS,CAAC,KAAK,EAAE,CAAC;AAC5B,CAAC;AAED,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;IACf,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACnB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC"}