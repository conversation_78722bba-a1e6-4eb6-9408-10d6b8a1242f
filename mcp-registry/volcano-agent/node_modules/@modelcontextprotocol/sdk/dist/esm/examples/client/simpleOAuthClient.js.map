{"version": 3, "file": "simpleOAuthClient.js", "sourceRoot": "", "sources": ["../../../../src/examples/client/simpleOAuthClient.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,WAAW,CAAC;AACzC,OAAO,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AAChD,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAC/B,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,6BAA6B,EAAE,MAAM,gCAAgC,CAAC;AAE/E,OAAO,EAAqC,oBAAoB,EAAE,qBAAqB,EAAE,MAAM,gBAAgB,CAAC;AAChH,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,2BAA2B,EAAE,MAAM,gCAAgC,CAAC;AAE7E,gBAAgB;AAChB,MAAM,kBAAkB,GAAG,2BAA2B,CAAC;AACvD,MAAM,aAAa,GAAG,IAAI,CAAC,CAAC,6CAA6C;AACzE,MAAM,YAAY,GAAG,oBAAoB,aAAa,WAAW,CAAC;AAElE;;;GAGG;AACH,MAAM,sBAAsB;IAOxB,YACY,SAAiB,EACjB,iBAA0B;QAD1B,cAAS,GAAT,SAAS,CAAQ;QACjB,sBAAiB,GAAjB,iBAAiB,CAAS;QAR9B,WAAM,GAAkB,IAAI,CAAC;QACpB,OAAE,GAAG,eAAe,CAAC;YAClC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;SACzB,CAAC,CAAC;IAKA,CAAC;IAEJ;;OAEG;IACK,KAAK,CAAC,QAAQ,CAAC,KAAa;QAChC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,GAAW;QACjC,OAAO,CAAC,GAAG,CAAC,yCAAyC,GAAG,EAAE,CAAC,CAAC;QAE5D,MAAM,OAAO,GAAG,SAAS,GAAG,GAAG,CAAC;QAEhC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;YAClB,IAAI,KAAK,EAAE,CAAC;gBACR,OAAO,CAAC,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC1D,OAAO,CAAC,GAAG,CAAC,yBAAyB,GAAG,EAAE,CAAC,CAAC;YAChD,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD;;;OAGG;IACH;;OAEG;IACK,KAAK,CAAC,oBAAoB;QAC9B,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACrC,0BAA0B;gBAC1B,IAAI,GAAG,CAAC,GAAG,KAAK,cAAc,EAAE,CAAC;oBAC7B,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACnB,GAAG,CAAC,GAAG,EAAE,CAAC;oBACV,OAAO;gBACX,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,yBAAyB,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;gBAChD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,EAAE,kBAAkB,CAAC,CAAC;gBAC7D,MAAM,IAAI,GAAG,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAChD,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAElD,IAAI,IAAI,EAAE,CAAC;oBACP,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;oBAC3E,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,cAAc,EAAE,WAAW,EAAE,CAAC,CAAC;oBACpD,GAAG,CAAC,GAAG,CAAC;;;;;;;;WAQjB,CAAC,CAAC;oBAEO,OAAO,CAAC,IAAI,CAAC,CAAC;oBACd,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC;gBAC3C,CAAC;qBAAM,IAAI,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,GAAG,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;oBAC/C,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,cAAc,EAAE,WAAW,EAAE,CAAC,CAAC;oBACpD,GAAG,CAAC,GAAG,CAAC;;;;4BAIA,KAAK;;;WAGtB,CAAC,CAAC;oBACO,MAAM,CAAC,IAAI,KAAK,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC9D,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;oBAC5D,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACnB,GAAG,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;oBACvB,MAAM,CAAC,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC,CAAC;gBACxD,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,EAAE;gBAC9B,OAAO,CAAC,GAAG,CAAC,qDAAqD,aAAa,EAAE,CAAC,CAAC;YACtF,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,aAA0C;QACtE,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC5D,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,MAAM,SAAS,GAAG,IAAI,6BAA6B,CAAC,OAAO,EAAE;YACzD,YAAY,EAAE,aAAa;SAC9B,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAEpC,IAAI,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;YAC9E,MAAM,IAAI,CAAC,MAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,KAAK,YAAY,iBAAiB,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;gBAChE,MAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBACpD,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC;gBACvC,MAAM,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,QAAQ,CAAC,CAAC;gBACzD,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;gBAC/D,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAChD,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;gBACjE,MAAM,KAAK,CAAC;YAChB,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC;QAEhE,MAAM,cAAc,GAAwB;YACxC,WAAW,EAAE,yBAAyB;YACtC,aAAa,EAAE,CAAC,YAAY,CAAC;YAC7B,WAAW,EAAE,CAAC,oBAAoB,EAAE,eAAe,CAAC;YACpD,cAAc,EAAE,CAAC,MAAM,CAAC;YACxB,0BAA0B,EAAE,oBAAoB;SACnD,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,MAAM,aAAa,GAAG,IAAI,2BAA2B,CACjD,YAAY,EACZ,cAAc,EACd,CAAC,WAAgB,EAAE,EAAE;YACjB,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;YAClE,OAAO,CAAC,GAAG,CAAC,uBAAuB,WAAW,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAC7D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7C,CAAC,EACD,IAAI,CAAC,iBAAiB,CACzB,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAEzC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CACpB;YACI,IAAI,EAAE,qBAAqB;YAC3B,OAAO,EAAE,OAAO;SACnB,EACD,EAAE,YAAY,EAAE,EAAE,EAAE,CACvB,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAEjC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAEzC,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAE5C,yBAAyB;QACzB,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACjB,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,8EAA8E,CAAC,CAAC;QAC5F,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,EAAE,CAAC;QAEd,OAAO,IAAI,EAAE,CAAC;YACV,IAAI,CAAC;gBACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAE7C,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;oBAClB,SAAS;gBACb,CAAC;gBAED,IAAI,OAAO,KAAK,MAAM,EAAE,CAAC;oBACrB,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;oBAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC;qBAAM,IAAI,OAAO,KAAK,MAAM,EAAE,CAAC;oBAC5B,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC3B,CAAC;qBAAM,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;oBACrC,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACvC,CAAC;qBAAM,IAAI,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBACvC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBACzC,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,GAAG,CAAC,oFAAoF,CAAC,CAAC;gBACtG,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;oBACvD,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;oBAC/B,MAAM;gBACV,CAAC;gBACD,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACrC,CAAC;QACL,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,SAAS;QACnB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,MAAM,OAAO,GAAqB;gBAC9B,MAAM,EAAE,YAAY;gBACpB,MAAM,EAAE,EAAE;aACb,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;YAEzE,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;gBACrC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;oBACjC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC1C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;wBACnB,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;oBACvD,CAAC;oBACD,OAAO,CAAC,GAAG,EAAE,CAAC;gBAClB,CAAC,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,OAAe;QACxC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAE1B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,OAAO;QACX,CAAC;QAED,4CAA4C;QAC5C,IAAI,QAAQ,GAA4B,EAAE,CAAC;QAC3C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnB,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5C,IAAI,CAAC;gBACD,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACtC,CAAC;YAAC,MAAM,CAAC;gBACL,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;gBAC1D,OAAO;YACX,CAAC;QACL,CAAC;QAED,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,QAAgB,EAAE,QAAiC;QACtE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,MAAM,OAAO,GAAoB;gBAC7B,MAAM,EAAE,YAAY;gBACpB,MAAM,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,SAAS,EAAE,QAAQ;iBACtB;aACJ,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;YAExE,OAAO,CAAC,GAAG,CAAC,cAAc,QAAQ,WAAW,CAAC,CAAC;YAC/C,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBAC7B,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC1B,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC9B,CAAC;yBAAM,CAAC;wBACJ,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBACzB,CAAC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACxB,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,QAAQ,IAAI,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAAe;QAC1C,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAE1B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,OAAO;QACX,CAAC;QAED,4CAA4C;QAC5C,IAAI,QAAQ,GAA4B,EAAE,CAAC;QAC3C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnB,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5C,IAAI,CAAC;gBACD,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACtC,CAAC;YAAC,MAAM,CAAC;gBACL,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;gBAC1D,OAAO;YACX,CAAC;QACL,CAAC;QAED,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB,EAAE,QAAiC;QACxE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,wEAAwE;YACxE,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,MAAM,CAAC,CAAC;YAEpD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,CACxD;gBACI,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,QAAQ;aACtB,EACD,oBAAoB,EACpB;gBACI,IAAI,EAAE;oBACF,MAAM,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;oBAC5B,GAAG,EAAE,KAAK;iBACb;aACJ,CACJ,CAAC;YAEF,wDAAwD;YACxD,IAAI,KAAK,EAAE,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC;gBACjC,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,aAAa;wBACd,OAAO,CAAC,GAAG,CAAC,mBAAmB,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;wBACtD,MAAM;oBAEV,KAAK,YAAY;wBACb,OAAO,CAAC,GAAG,CAAC,aAAa,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;wBAChD,IAAI,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;4BAC7B,OAAO,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;wBACnD,CAAC;wBACD,MAAM;oBAEV,KAAK,QAAQ;wBACT,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;wBAC5B,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;4BACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gCAC1B,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;4BAC9B,CAAC;iCAAM,CAAC;gCACJ,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;4BACzB,CAAC;wBACL,CAAC,CAAC,CAAC;wBACH,MAAM;oBAEV,KAAK,OAAO;wBACR,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;wBACxB,OAAO,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC1C,MAAM;gBACd,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,4BAA4B,QAAQ,IAAI,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC;IACL,CAAC;IAED,KAAK;QACD,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;QAChB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,yEAAyE;YACzE,sDAAsD;QAC1D,CAAC;IACL,CAAC;CACJ;AAED;;GAEG;AACH,KAAK,UAAU,IAAI;IACf,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACnC,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC;IAChD,MAAM,iBAAiB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAElC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC1C,OAAO,CAAC,GAAG,CAAC,kBAAkB,SAAS,EAAE,CAAC,CAAC;IAC3C,IAAI,iBAAiB,EAAE,CAAC;QACpB,OAAO,CAAC,GAAG,CAAC,wBAAwB,iBAAiB,EAAE,CAAC,CAAC;IAC7D,CAAC;IACD,OAAO,CAAC,GAAG,EAAE,CAAC;IAEd,MAAM,MAAM,GAAG,IAAI,sBAAsB,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;IAExE,2BAA2B;IAC3B,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC/B,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC;QACD,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;YAAS,CAAC;QACP,MAAM,CAAC,KAAK,EAAE,CAAC;IACnB,CAAC;AACL,CAAC;AAED,wCAAwC;AACxC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;IACjB,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;IACzC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC"}