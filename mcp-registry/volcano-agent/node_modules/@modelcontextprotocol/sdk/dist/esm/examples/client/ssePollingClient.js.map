{"version": 3, "file": "ssePollingClient.js", "sourceRoot": "", "sources": ["../../../../src/examples/client/ssePollingClient.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AACH,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,6BAA6B,EAAE,MAAM,gCAAgC,CAAC;AAC/E,OAAO,EAAE,oBAAoB,EAAE,gCAAgC,EAAE,MAAM,gBAAgB,CAAC;AAExF,MAAM,UAAU,GAAG,2BAA2B,CAAC;AAE/C,KAAK,UAAU,IAAI;IACf,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC1C,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC1C,OAAO,CAAC,GAAG,CAAC,iBAAiB,UAAU,KAAK,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,6CAA6C;IAC7C,MAAM,SAAS,GAAG,IAAI,6BAA6B,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,EAAE;IACrE,wEAAwE;KAC3E,CAAC,CAAC;IAEH,wCAAwC;IACxC,IAAI,WAA+B,CAAC;IAEpC,2DAA2D;IAC3D,mDAAmD;IACnD,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE;QACxB,6CAA6C;QAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO;QACjD,2DAA2D;QAC3D,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,wBAAwB,CAAC,EAAE,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;YAChF,OAAO;QACX,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IACvD,CAAC,CAAC;IAEF,iCAAiC;IACjC,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;QACrB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IACjD,CAAC,CAAC;IAEF,4BAA4B;IAC5B,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC;QACtB,IAAI,EAAE,oBAAoB;QAC1B,OAAO,EAAE,OAAO;KACnB,CAAC,CAAC;IAEH,0DAA0D;IAC1D,MAAM,CAAC,sBAAsB,CAAC,gCAAgC,EAAE,YAAY,CAAC,EAAE;QAC3E,MAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC;QACD,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,0BAA0B;QAC1B,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;QAC/E,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAC/B;YACI,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE;gBACJ,IAAI,EAAE,WAAW;gBACjB,SAAS,EAAE,EAAE;aAChB;SACJ,EACD,oBAAoB,EACpB;YACI,wCAAwC;YACxC,iBAAiB,EAAE,KAAK,CAAC,EAAE;gBACvB,WAAW,GAAG,KAAK,CAAC;gBACpB,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,EAAE,CAAC,CAAC;YACvC,CAAC;SACJ,CACJ,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,2BAA2B,WAAW,EAAE,CAAC,CAAC;IAC1D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IACpC,CAAC;YAAS,CAAC;QACP,MAAM,SAAS,CAAC,KAAK,EAAE,CAAC;QACxB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACzC,CAAC;AACL,CAAC;AAED,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}