{"version": 3, "file": "demoInMemoryOAuthProvider.js", "sourceRoot": "", "sources": ["../../../../src/examples/server/demoInMemoryOAuthProvider.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAIzC,OAAO,OAA8B,MAAM,SAAS,CAAC;AAErD,OAAO,EAAE,mBAAmB,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AACjF,OAAO,EAAE,wBAAwB,EAAE,MAAM,4BAA4B,CAAC;AACtE,OAAO,EAAE,mBAAmB,EAAE,MAAM,6BAA6B,CAAC;AAElE,MAAM,OAAO,wBAAwB;IAArC;QACY,YAAO,GAAG,IAAI,GAAG,EAAsC,CAAC;IAUpE,CAAC;IARG,KAAK,CAAC,SAAS,CAAC,QAAgB;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,cAA0C;QAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAC3D,OAAO,cAAc,CAAC;IAC1B,CAAC;CACJ;AAED;;;;;;;GAOG;AACH,MAAM,OAAO,wBAAwB;IAWjC,YAAoB,gBAA8C;QAA9C,qBAAgB,GAAhB,gBAAgB,CAA8B;QAVlE,iBAAY,GAAG,IAAI,wBAAwB,EAAE,CAAC;QACtC,UAAK,GAAG,IAAI,GAAG,EAMpB,CAAC;QACI,WAAM,GAAG,IAAI,GAAG,EAAoB,CAAC;IAEwB,CAAC;IAEtE,KAAK,CAAC,SAAS,CAAC,MAAkC,EAAE,MAA2B,EAAE,GAAa;QAC1F,MAAM,IAAI,GAAG,UAAU,EAAE,CAAC;QAE1B,MAAM,YAAY,GAAG,IAAI,eAAe,CAAC;YACrC,IAAI;SACP,CAAC,CAAC;QACH,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC7B,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;YACjB,MAAM;YACN,MAAM;SACT,CAAC,CAAC;QAEH,wBAAwB;QACxB,gEAAgE;QAChE,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACb,MAAM,cAAc,GAAG;gBACnB,MAAM,EAAE,WAAW;gBACnB,IAAI,EAAE,WAAW;gBACjB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC;YACF,GAAG,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;gBACvD,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,KAAK,EAAE,qCAAqC;gBACpD,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,+BAA+B;gBAC5D,IAAI,EAAE,GAAG,CAAC,0BAA0B;aACvC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,CAAC,CAAC;QAC/D,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC9C,SAAS,CAAC,MAAM,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC3C,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,6BAA6B,CAAC,MAAkC,EAAE,iBAAyB;QAC7F,yCAAyC;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,yBAAyB,CAC3B,MAAkC,EAClC,iBAAyB;IACzB,wDAAwD;IACxD,oCAAoC;IACpC,aAAsB;QAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,qDAAqD,QAAQ,CAAC,MAAM,CAAC,SAAS,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;QAC7H,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG,UAAU,EAAE,CAAC;QAE3B,MAAM,SAAS,GAAG;YACd,KAAK;YACL,QAAQ,EAAE,MAAM,CAAC,SAAS;YAC1B,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE;YACpC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,EAAE,SAAS;YAC1C,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,QAAQ;YAClC,IAAI,EAAE,QAAQ;SACjB,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAElC,OAAO;YACH,YAAY,EAAE,KAAK;YACnB,UAAU,EAAE,QAAQ;YACpB,UAAU,EAAE,IAAI;YAChB,KAAK,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;SAClD,CAAC;IACN,CAAC;IAED,KAAK,CAAC,oBAAoB,CACtB,OAAmC,EACnC,aAAqB,EACrB,OAAkB,EAClB,SAAe;QAEf,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,KAAa;QACjC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YACzE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAChD,CAAC;QAED,OAAO;YACH,KAAK;YACL,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;YACjD,QAAQ,EAAE,SAAS,CAAC,QAAQ;SAC/B,CAAC;IACN,CAAC;CACJ;AAED,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,EAC5B,aAAa,EACb,YAAY,EACZ,cAAc,EAKjB,EAAiB,EAAE;IAChB,kCAAkC;IAClC,gEAAgE;IAChE,gEAAgE;IAChE,wEAAwE;IACxE,wBAAwB;IAExB,MAAM,gBAAgB,GAAG,cAAc;QACnC,CAAC,CAAC,CAAC,QAAc,EAAE,EAAE;YACf,IAAI,CAAC,QAAQ;gBAAE,OAAO,KAAK,CAAC;YAC5B,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,YAAY,CAAC,CAAC;YAChE,OAAO,QAAQ,CAAC,QAAQ,EAAE,KAAK,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QAC/D,CAAC;QACH,CAAC,CAAC,SAAS,CAAC;IAEhB,MAAM,QAAQ,GAAG,IAAI,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;IAChE,MAAM,OAAO,GAAG,OAAO,EAAE,CAAC;IAC1B,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IAC5B,6BAA6B;IAC7B,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;IAElC,sCAAsC;IACtC,gEAAgE;IAChE,qCAAqC;IACrC,OAAO,CAAC,GAAG,CACP,aAAa,CAAC;QACV,QAAQ;QACR,SAAS,EAAE,aAAa;QACxB,eAAe,EAAE,CAAC,WAAW,CAAC;KACjC,CAAC,CACL,CAAC;IAEF,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QAC9D,IAAI,CAAC;YACD,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YAC3B,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC,CAAC;gBACrD,OAAO;YACX,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC1D,GAAG,CAAC,IAAI,CAAC;gBACL,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE,SAAS,CAAC,QAAQ;gBAC7B,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;gBACjC,GAAG,EAAE,SAAS,CAAC,SAAS;gBACxB,GAAG,EAAE,SAAS,CAAC,QAAQ;aAC1B,CAAC,CAAC;YACH,OAAO;QACX,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACjB,MAAM,EAAE,KAAK;gBACb,KAAK,EAAE,cAAc;gBACrB,iBAAiB,EAAE,kBAAkB,KAAK,EAAE;aAC/C,CAAC,CAAC;QACP,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC;IACrC,wBAAwB;IACxB,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE;QAC9B,IAAI,KAAK,EAAE,CAAC;YACR,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,gDAAgD,SAAS,EAAE,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;IAEH,gEAAgE;IAChE,iDAAiD;IACjD,MAAM,aAAa,GAAkB,mBAAmB,CAAC;QACrD,QAAQ;QACR,SAAS,EAAE,aAAa;QACxB,eAAe,EAAE,CAAC,WAAW,CAAC;KACjC,CAAC,CAAC;IAEH,aAAa,CAAC,sBAAsB,GAAG,IAAI,GAAG,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC;IAElF,OAAO,aAAa,CAAC;AACzB,CAAC,CAAC"}