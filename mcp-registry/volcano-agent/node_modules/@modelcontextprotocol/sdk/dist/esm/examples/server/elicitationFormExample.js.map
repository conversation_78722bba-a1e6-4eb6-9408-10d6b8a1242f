{"version": 3, "file": "elicitationFormExample.js", "sourceRoot": "", "sources": ["../../../../src/examples/server/elicitationFormExample.ts"], "names": [], "mappings": "AAAA,kEAAkE;AAClE,EAAE;AACF,yFAAyF;AACzF,0EAA0E;AAC1E,2FAA2F;AAC3F,gCAAgC;AAChC,kFAAkF;AAClF,mDAAmD;AAEnD,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAEzC,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAChD,OAAO,EAAE,6BAA6B,EAAE,MAAM,gCAAgC,CAAC;AAC/E,OAAO,EAAE,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,yBAAyB,CAAC;AAE9D,8FAA8F;AAC9F,2FAA2F;AAC3F,MAAM,SAAS,GAAG,IAAI,SAAS,CAC3B;IACI,IAAI,EAAE,iCAAiC;IACvC,OAAO,EAAE,OAAO;CACnB,EACD;IACI,YAAY,EAAE,EAAE;CACnB,CACJ,CAAC;AAEF;;;GAGG;AACH,SAAS,CAAC,YAAY,CAClB,eAAe,EACf;IACI,WAAW,EAAE,6DAA6D;IAC1E,WAAW,EAAE,EAAE;CAClB,EACD,KAAK,IAAI,EAAE;IACP,IAAI,CAAC;QACD,oDAAoD;QACpD,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC;YAC9C,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,+CAA+C;YACxD,eAAe,EAAE;gBACb,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,QAAQ,EAAE;wBACN,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,UAAU;wBACjB,WAAW,EAAE,yCAAyC;wBACtD,SAAS,EAAE,CAAC;wBACZ,SAAS,EAAE,EAAE;qBAChB;oBACD,KAAK,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,OAAO;wBACd,WAAW,EAAE,oBAAoB;wBACjC,MAAM,EAAE,OAAO;qBAClB;oBACD,QAAQ,EAAE;wBACN,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,UAAU;wBACjB,WAAW,EAAE,kCAAkC;wBAC/C,SAAS,EAAE,CAAC;qBACf;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,SAAS;wBACf,KAAK,EAAE,YAAY;wBACnB,WAAW,EAAE,0BAA0B;wBACvC,OAAO,EAAE,KAAK;qBACjB;iBACJ;gBACD,QAAQ,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC;aAC9C;SACJ,CAAC,CAAC;QAEH,wCAAwC;QACxC,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YAC/C,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC,OAK9C,CAAC;YAEF,OAAO;gBACH,OAAO,EAAE;oBACL;wBACI,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,yCAAyC,QAAQ,YAAY,KAAK,iBAAiB,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;qBACvH;iBACJ;aACJ,CAAC;QACN,CAAC;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACrC,OAAO;gBACH,OAAO,EAAE;oBACL;wBACI,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,iCAAiC;qBAC1C;iBACJ;aACJ,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,OAAO;gBACH,OAAO,EAAE;oBACL;wBACI,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,6BAA6B;qBACtC;iBACJ;aACJ,CAAC;QACN,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO;YACH,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBACzF;aACJ;YACD,OAAO,EAAE,IAAI;SAChB,CAAC;IACN,CAAC;AACL,CAAC,CACJ,CAAC;AAEF;;;GAGG;AACH,SAAS,CAAC,YAAY,CAClB,cAAc,EACd;IACI,WAAW,EAAE,qDAAqD;IAClE,WAAW,EAAE,EAAE;CAClB,EACD,KAAK,IAAI,EAAE;IACP,IAAI,CAAC;QACD,0CAA0C;QAC1C,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC;YACjD,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,uCAAuC;YAChD,eAAe,EAAE;gBACb,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,KAAK,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,aAAa;wBACpB,WAAW,EAAE,mBAAmB;wBAChC,SAAS,EAAE,CAAC;qBACf;oBACD,WAAW,EAAE;wBACT,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,aAAa;wBACpB,WAAW,EAAE,8BAA8B;qBAC9C;iBACJ;gBACD,QAAQ,EAAE,CAAC,OAAO,CAAC;aACtB;SACJ,CAAC,CAAC;QAEH,IAAI,SAAS,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACtD,OAAO;gBACH,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,2BAA2B,EAAE,CAAC;aACjE,CAAC;QACN,CAAC;QAED,gCAAgC;QAChC,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC;YAChD,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,6BAA6B;YACtC,eAAe,EAAE;gBACb,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,IAAI,EAAE;wBACF,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,MAAM;wBACb,WAAW,EAAE,YAAY;wBACzB,MAAM,EAAE,MAAM;qBACjB;oBACD,SAAS,EAAE;wBACP,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,YAAY;wBACnB,WAAW,EAAE,0BAA0B;qBAC1C;oBACD,QAAQ,EAAE;wBACN,IAAI,EAAE,SAAS;wBACf,KAAK,EAAE,UAAU;wBACjB,WAAW,EAAE,qBAAqB;wBAClC,OAAO,EAAE,EAAE;wBACX,OAAO,EAAE,GAAG;qBACf;iBACJ;gBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,UAAU,CAAC;aAC9C;SACJ,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACpD,OAAO;gBACH,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,2BAA2B,EAAE,CAAC;aACjE,CAAC;QACN,CAAC;QAED,oCAAoC;QACpC,MAAM,KAAK,GAAG;YACV,GAAG,SAAS,CAAC,OAAO;YACpB,GAAG,QAAQ,CAAC,OAAO;SACtB,CAAC;QAEF,OAAO;YACH,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,kCAAkC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;iBAC3E;aACJ;SACJ,CAAC;IACN,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO;YACH,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBAC3F;aACJ;YACD,OAAO,EAAE,IAAI;SAChB,CAAC;IACN,CAAC;AACL,CAAC,CACJ,CAAC;AAEF;;;GAGG;AACH,SAAS,CAAC,YAAY,CAClB,yBAAyB,EACzB;IACI,WAAW,EAAE,yCAAyC;IACtD,WAAW,EAAE,EAAE;CAClB,EACD,KAAK,IAAI,EAAE;IACP,IAAI,CAAC;QACD,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC;YAC9C,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,uCAAuC;YAChD,eAAe,EAAE;gBACb,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,IAAI,EAAE;wBACF,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,WAAW;wBAClB,WAAW,EAAE,gBAAgB;wBAC7B,SAAS,EAAE,CAAC;qBACf;oBACD,MAAM,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,gBAAgB;wBACvB,SAAS,EAAE,CAAC;qBACf;oBACD,IAAI,EAAE;wBACF,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,MAAM;wBACb,SAAS,EAAE,CAAC;qBACf;oBACD,KAAK,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,gBAAgB;wBACvB,SAAS,EAAE,CAAC;wBACZ,SAAS,EAAE,CAAC;qBACf;oBACD,OAAO,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,iBAAiB;wBACxB,WAAW,EAAE,kBAAkB;qBAClC;oBACD,KAAK,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,yBAAyB;wBAChC,WAAW,EAAE,sBAAsB;qBACtC;iBACJ;gBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;aAC3D;SACJ,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YAC/C,OAAO;gBACH,OAAO,EAAE;oBACL;wBACI,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,oCAAoC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;qBACtF;iBACJ;aACJ,CAAC;QACN,CAAC;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACrC,OAAO;gBACH,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,mCAAmC,EAAE,CAAC;aACzE,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,OAAO;gBACH,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,+BAA+B,EAAE,CAAC;aACrE,CAAC;QACN,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO;YACH,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBAC3F;aACJ;YACD,OAAO,EAAE,IAAI;SAChB,CAAC;IACN,CAAC;AACL,CAAC,CACJ,CAAC;AAEF,KAAK,UAAU,IAAI;IACf,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAEtE,MAAM,GAAG,GAAG,mBAAmB,EAAE,CAAC;IAElC,wCAAwC;IACxC,MAAM,UAAU,GAA2D,EAAE,CAAC;IAE9E,oBAAoB;IACpB,MAAM,cAAc,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QACzD,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAuB,CAAC;QACtE,IAAI,SAAS,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,CAAC,qCAAqC,SAAS,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC;YACD,IAAI,SAAwC,CAAC;YAC7C,IAAI,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBACrC,4CAA4C;gBAC5C,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;YACtC,CAAC;iBAAM,IAAI,CAAC,SAAS,IAAI,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrD,oDAAoD;gBACpD,SAAS,GAAG,IAAI,6BAA6B,CAAC;oBAC1C,kBAAkB,EAAE,GAAG,EAAE,CAAC,UAAU,EAAE;oBACtC,oBAAoB,EAAE,SAAS,CAAC,EAAE;wBAC9B,gEAAgE;wBAChE,OAAO,CAAC,GAAG,CAAC,gCAAgC,SAAS,EAAE,CAAC,CAAC;wBACzD,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;oBACtC,CAAC;iBACJ,CAAC,CAAC;gBAEH,2DAA2D;gBAC3D,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;oBACrB,MAAM,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC;oBAChC,IAAI,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBACzB,OAAO,CAAC,GAAG,CAAC,gCAAgC,GAAG,gCAAgC,CAAC,CAAC;wBACjF,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;oBAC3B,CAAC;gBACL,CAAC,CAAC;gBAEF,sEAAsE;gBACtE,MAAM,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAEnC,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;gBAClD,OAAO;YACX,CAAC;iBAAM,CAAC;gBACJ,gEAAgE;gBAChE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACjB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACH,IAAI,EAAE,CAAC,KAAK;wBACZ,OAAO,EAAE,2CAA2C;qBACvD;oBACD,EAAE,EAAE,IAAI;iBACX,CAAC,CAAC;gBACH,OAAO;YACX,CAAC;YAED,6CAA6C;YAC7C,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACnB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACjB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACH,IAAI,EAAE,CAAC,KAAK;wBACZ,OAAO,EAAE,uBAAuB;qBACnC;oBACD,EAAE,EAAE,IAAI;iBACX,CAAC,CAAC;YACP,CAAC;QACL,CAAC;IACL,CAAC,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;IAEjC,sCAAsC;IACtC,MAAM,aAAa,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QACxD,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAuB,CAAC;QACtE,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YACtD,OAAO;QACX,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,uCAAuC,SAAS,EAAE,CAAC,CAAC;QAChE,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;QACxC,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAC5C,CAAC,CAAC;IAEF,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAE/B,iDAAiD;IACjD,MAAM,gBAAgB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QAC3D,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAuB,CAAC;QACtE,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YACtD,OAAO;QACX,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,oDAAoD,SAAS,EAAE,CAAC,CAAC;QAE7E,IAAI,CAAC;YACD,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACnB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACjE,CAAC;QACL,CAAC;IACL,CAAC,CAAC;IAEF,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;IAErC,kBAAkB;IAClB,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;QACrB,IAAI,KAAK,EAAE,CAAC;YACR,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,kEAAkE,IAAI,MAAM,CAAC,CAAC;QAC1F,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,oEAAoE,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;IAEH,yBAAyB;IACzB,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;QAC5B,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QAEvC,6DAA6D;QAC7D,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACjC,IAAI,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC,iCAAiC,SAAS,EAAE,CAAC,CAAC;gBAC1D,MAAM,UAAU,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC;gBACpC,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC;YACjC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,uCAAuC,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;AACP,CAAC;AAED,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;IACjB,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;IACtC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC"}