{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../../../src/experimental/tasks/client.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAC/D,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AACvE,OAAO,KAAK,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAChF,OAAO,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACpG,OAAO,EAAE,oBAAoB,EAAE,KAAK,iCAAiC,EAAuB,MAAM,gBAAgB,CAAC;AAEnH,OAAO,KAAK,EAAE,aAAa,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAgBnF;;;;;;;;;;GAUG;AACH,qBAAa,uBAAuB,CAChC,QAAQ,SAAS,OAAO,GAAG,OAAO,EAClC,aAAa,SAAS,YAAY,GAAG,YAAY,EACjD,OAAO,SAAS,MAAM,GAAG,MAAM;IAEnB,OAAO,CAAC,QAAQ,CAAC,OAAO;gBAAP,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC;IAE9E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACI,cAAc,CAAC,CAAC,SAAS,OAAO,oBAAoB,GAAG,OAAO,iCAAiC,EAClG,MAAM,EAAE,eAAe,CAAC,QAAQ,CAAC,EACjC,YAAY,GAAE,CAA6B,EAC3C,OAAO,CAAC,EAAE,cAAc,GACzB,cAAc,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;IAyE/D;;;;;;;;OAQG;IACG,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC,aAAa,CAAC;IAM/E;;;;;;;;;OASG;IACG,aAAa,CAAC,CAAC,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAapI;;;;;;;;OAQG;IACG,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC,eAAe,CAAC;IASpF;;;;;;;OAOG;IACG,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC;IASrF;;;;;;;;;;;;;OAaG;IACH,aAAa,CAAC,CAAC,SAAS,eAAe,EACnC,OAAO,EAAE,aAAa,GAAG,QAAQ,EACjC,YAAY,EAAE,CAAC,EACf,OAAO,CAAC,EAAE,cAAc,GACzB,cAAc,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;CAWlE"}