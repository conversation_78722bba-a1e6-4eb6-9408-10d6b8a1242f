{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../../../src/experimental/tasks/client.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAOH,OAAO,EAAE,oBAAoB,EAA0C,QAAQ,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAkBnH;;;;;;;;;;GAUG;AACH,MAAM,OAAO,uBAAuB;IAKhC,YAA6B,OAAiD;QAAjD,YAAO,GAAP,OAAO,CAA0C;IAAG,CAAC;IAElF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACH,KAAK,CAAC,CAAC,cAAc,CACjB,MAAiC,EACjC,eAAkB,oBAAyB,EAC3C,OAAwB;QAExB,mCAAmC;QACnC,MAAM,cAAc,GAAG,IAAI,CAAC,OAA8C,CAAC;QAE3E,iFAAiF;QACjF,MAAM,eAAe,GAAG;YACpB,GAAG,OAAO;YACV,mFAAmF;YACnF,mEAAmE;YACnE,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;SACnF,CAAC;QAEF,MAAM,MAAM,GAAG,cAAc,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;QAE7G,+DAA+D;QAC/D,MAAM,SAAS,GAAG,cAAc,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAErE,qEAAqE;QACrE,IAAI,KAAK,EAAE,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC;YACjC,6EAA6E;YAC7E,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACzC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAE9B,oFAAoF;gBACpF,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBAC/C,MAAM;wBACF,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,IAAI,QAAQ,CACf,SAAS,CAAC,cAAc,EACxB,QAAQ,MAAM,CAAC,IAAI,6DAA6D,CACnF;qBACJ,CAAC;oBACF,OAAO;gBACX,CAAC;gBAED,0EAA0E;gBAC1E,IAAI,MAAM,CAAC,iBAAiB,EAAE,CAAC;oBAC3B,IAAI,CAAC;wBACD,qDAAqD;wBACrD,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;wBAE7D,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;4BAC1B,MAAM;gCACF,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE,IAAI,QAAQ,CACf,SAAS,CAAC,aAAa,EACvB,+DAA+D,gBAAgB,CAAC,YAAY,EAAE,CACjG;6BACJ,CAAC;4BACF,OAAO;wBACX,CAAC;oBACL,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACb,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;4BAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;4BAC/B,OAAO;wBACX,CAAC;wBACD,MAAM;4BACF,IAAI,EAAE,OAAO;4BACb,KAAK,EAAE,IAAI,QAAQ,CACf,SAAS,CAAC,aAAa,EACvB,0CAA0C,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACrG;yBACJ,CAAC;wBACF,OAAO;oBACX,CAAC;gBACL,CAAC;YACL,CAAC;YAED,wEAAwE;YACxE,MAAM,OAAO,CAAC;QAClB,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,OAAO,CAAC,MAAc,EAAE,OAAwB;QAGlD,OAAQ,IAAI,CAAC,OAAwC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;IACvF,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,aAAa,CAA4B,MAAc,EAAE,YAAgB,EAAE,OAAwB;QACrG,sDAAsD;QACtD,OACI,IAAI,CAAC,OAOR,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,SAAS,CAAC,MAAe,EAAE,OAAwB;QACrD,sDAAsD;QACtD,OACI,IAAI,CAAC,OAGR,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,OAAwB;QACrD,sDAAsD;QACtD,OACI,IAAI,CAAC,OAGR,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,aAAa,CACT,OAAiC,EACjC,YAAe,EACf,OAAwB;QAUxB,OAAQ,IAAI,CAAC,OAA8C,CAAC,aAAa,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;IAC9G,CAAC;CACJ"}