{"version": 3, "file": "interfaces.d.ts", "sourceRoot": "", "sources": ["../../../../src/experimental/tasks/interfaces.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EACH,IAAI,EACJ,SAAS,EACT,MAAM,EACN,cAAc,EACd,mBAAmB,EACnB,qBAAqB,EACrB,oBAAoB,EACpB,aAAa,EACb,kBAAkB,EAClB,cAAc,EACd,aAAa,EACb,aAAa,EACb,OAAO,EACV,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAC9C,OAAO,KAAK,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AACtF,OAAO,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AAM5F;;;GAGG;AACH,MAAM,WAAW,6BAA8B,SAAQ,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC;IACzG,SAAS,EAAE,gBAAgB,CAAC;CAC/B;AAED;;;GAGG;AACH,MAAM,WAAW,uBAAwB,SAAQ,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC;IACnG,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,gBAAgB,CAAC;CAC/B;AAED;;;GAGG;AACH,MAAM,MAAM,gBAAgB,CACxB,WAAW,SAAS,MAAM,EAC1B,MAAM,SAAS,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,EACrE,IAAI,SAAS,SAAS,GAAG,iBAAiB,GAAG,SAAS,GAAG,SAAS,IAClE,IAAI,SAAS,iBAAiB,GAC5B,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GAC9E,IAAI,SAAS,SAAS,GACpB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,KAAK,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GACpE,CAAC,KAAK,EAAE,MAAM,KAAK,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;AAE9D;;;GAGG;AACH,MAAM,MAAM,wBAAwB,CAChC,WAAW,SAAS,MAAM,EAC1B,IAAI,SAAS,SAAS,GAAG,iBAAiB,GAAG,SAAS,GAAG,SAAS,IAClE,gBAAgB,CAAC,WAAW,EAAE,6BAA6B,EAAE,IAAI,CAAC,CAAC;AAEvE;;;GAGG;AACH,MAAM,MAAM,kBAAkB,CAC1B,WAAW,SAAS,MAAM,EAC1B,IAAI,SAAS,SAAS,GAAG,iBAAiB,GAAG,SAAS,GAAG,SAAS,IAClE,gBAAgB,CAAC,WAAW,EAAE,uBAAuB,EAAE,IAAI,CAAC,CAAC;AAEjE;;;GAGG;AACH,MAAM,WAAW,eAAe,CAAC,IAAI,SAAS,SAAS,GAAG,iBAAiB,GAAG,SAAS,GAAG,SAAS;IAC/F,UAAU,EAAE,wBAAwB,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC7D,OAAO,EAAE,kBAAkB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IACjD,aAAa,EAAE,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;CAC3D;AAED;;;;GAIG;AACH,MAAM,MAAM,iBAAiB,CAAC,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,GAAG;IAC7G,WAAW,EAAE,WAAW,SAAS,WAAW,GAAG,SAAS,GAAG,KAAK,GAAG,WAAW,CAAC;CAClF,CAAC;AAEF;;;;;GAKG;AACH,MAAM,MAAM,aAAa,GAAG,aAAa,GAAG,kBAAkB,GAAG,cAAc,GAAG,WAAW,CAAC;AAE9F,MAAM,WAAW,iBAAiB;IAC9B,sBAAsB;IACtB,IAAI,EAAE,MAAM,CAAC;IACb,6DAA6D;IAC7D,SAAS,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,aAAc,SAAQ,iBAAiB;IACpD,IAAI,EAAE,SAAS,CAAC;IAChB,iCAAiC;IACjC,OAAO,EAAE,cAAc,CAAC;CAC3B;AAED,MAAM,WAAW,kBAAmB,SAAQ,iBAAiB;IACzD,IAAI,EAAE,cAAc,CAAC;IACrB,sCAAsC;IACtC,OAAO,EAAE,mBAAmB,CAAC;CAChC;AAED,MAAM,WAAW,cAAe,SAAQ,iBAAiB;IACrD,IAAI,EAAE,UAAU,CAAC;IACjB,kCAAkC;IAClC,OAAO,EAAE,qBAAqB,CAAC;CAClC;AAED,MAAM,WAAW,WAAY,SAAQ,iBAAiB;IAClD,IAAI,EAAE,OAAO,CAAC;IACd,+BAA+B;IAC/B,OAAO,EAAE,oBAAoB,CAAC;CACjC;AAED;;;;;;;;;;;;;;GAcG;AACH,MAAM,WAAW,gBAAgB;IAC7B;;;;;;;;OAQG;IACH,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAErG;;;;;OAKG;IACH,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,GAAG,SAAS,CAAC,CAAC;IAEhF;;;;;;OAMG;IACH,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;CAC5E;AAED;;;GAGG;AACH,MAAM,WAAW,iBAAiB;IAC9B;;;OAGG;IACH,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEpB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CACrC;AAED;;;;;;;GAOG;AACH,MAAM,WAAW,SAAS;IACtB;;;;;;;;;;;;;;;;OAgBG;IACH,UAAU,CAAC,UAAU,EAAE,iBAAiB,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAErH;;;;;;OAMG;IACH,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IAElE;;;;;;;OAOG;IACH,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,GAAG,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEnH;;;;;;OAMG;IACH,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAEnE;;;;;;;OAOG;IACH,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEpH;;;;;;OAMG;IACH,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,KAAK,EAAE,IAAI,EAAE,CAAC;QAAC,UAAU,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;CACnG;AAED;;;;;;;GAOG;AACH,wBAAgB,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,OAAO,CAE1D"}