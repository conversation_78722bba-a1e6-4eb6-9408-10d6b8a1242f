{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../../../src/server/auth/errors.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,KAAK;IAGjC,YACI,OAAe,EACC,QAAiB;QAEjC,KAAK,CAAC,OAAO,CAAC,CAAC;QAFC,aAAQ,GAAR,QAAQ,CAAS;QAGjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACZ,MAAM,QAAQ,GAAuB;YACjC,KAAK,EAAE,IAAI,CAAC,SAAS;YACrB,iBAAiB,EAAE,IAAI,CAAC,OAAO;SAClC,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;QACvC,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,IAAI,SAAS;QACT,OAAQ,IAAI,CAAC,WAAiC,CAAC,SAAS,CAAC;IAC7D,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,mBAAoB,SAAQ,UAAU;;AACxC,6BAAS,GAAG,iBAAiB,CAAC;AAGzC;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,UAAU;;AACvC,4BAAS,GAAG,gBAAgB,CAAC;AAGxC;;;;GAIG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;;AACtC,2BAAS,GAAG,eAAe,CAAC;AAGvC;;;GAGG;AACH,MAAM,OAAO,uBAAwB,SAAQ,UAAU;;AAC5C,iCAAS,GAAG,qBAAqB,CAAC;AAG7C;;;GAGG;AACH,MAAM,OAAO,yBAA0B,SAAQ,UAAU;;AAC9C,mCAAS,GAAG,wBAAwB,CAAC;AAGhD;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;;AACtC,2BAAS,GAAG,eAAe,CAAC;AAGvC;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;;AACtC,2BAAS,GAAG,eAAe,CAAC;AAGvC;;;GAGG;AACH,MAAM,OAAO,WAAY,SAAQ,UAAU;;AAChC,qBAAS,GAAG,cAAc,CAAC;AAGtC;;;GAGG;AACH,MAAM,OAAO,2BAA4B,SAAQ,UAAU;;AAChD,qCAAS,GAAG,yBAAyB,CAAC;AAGjD;;;GAGG;AACH,MAAM,OAAO,4BAA6B,SAAQ,UAAU;;AACjD,sCAAS,GAAG,2BAA2B,CAAC;AAGnD;;;GAGG;AACH,MAAM,OAAO,yBAA0B,SAAQ,UAAU;;AAC9C,mCAAS,GAAG,wBAAwB,CAAC;AAGhD;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;;AACtC,2BAAS,GAAG,eAAe,CAAC;AAGvC;;;GAGG;AACH,MAAM,OAAO,qBAAsB,SAAQ,UAAU;;AAC1C,+BAAS,GAAG,oBAAoB,CAAC;AAG5C;;;GAGG;AACH,MAAM,OAAO,oBAAqB,SAAQ,UAAU;;AACzC,8BAAS,GAAG,mBAAmB,CAAC;AAG3C;;;GAGG;AACH,MAAM,OAAO,0BAA2B,SAAQ,UAAU;;AAC/C,oCAAS,GAAG,yBAAyB,CAAC;AAGjD;;GAEG;AACH,MAAM,OAAO,sBAAuB,SAAQ,UAAU;;AAC3C,gCAAS,GAAG,oBAAoB,CAAC;AAG5C;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,UAAU;;AACvC,4BAAS,GAAG,gBAAgB,CAAC;AAGxC;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,UAAU;IAC5C,YACqB,eAAuB,EACxC,OAAe,EACf,QAAiB;QAEjB,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAJR,oBAAe,GAAf,eAAe,CAAQ;IAK5C,CAAC;IAED,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG;IACxB,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,mBAAmB;IACpD,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,kBAAkB;IAClD,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,uBAAuB,CAAC,SAAS,CAAC,EAAE,uBAAuB;IAC5D,CAAC,yBAAyB,CAAC,SAAS,CAAC,EAAE,yBAAyB;IAChE,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,WAAW;IACpC,CAAC,2BAA2B,CAAC,SAAS,CAAC,EAAE,2BAA2B;IACpE,CAAC,4BAA4B,CAAC,SAAS,CAAC,EAAE,4BAA4B;IACtE,CAAC,yBAAyB,CAAC,SAAS,CAAC,EAAE,yBAAyB;IAChE,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,qBAAqB;IACxD,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE,oBAAoB;IACtD,CAAC,0BAA0B,CAAC,SAAS,CAAC,EAAE,0BAA0B;IAClE,CAAC,sBAAsB,CAAC,SAAS,CAAC,EAAE,sBAAsB;IAC1D,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,kBAAkB;CAC5C,CAAC"}