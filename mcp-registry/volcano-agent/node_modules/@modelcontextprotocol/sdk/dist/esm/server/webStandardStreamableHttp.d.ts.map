{"version": 3, "file": "webStandardStreamableHttp.d.ts", "sourceRoot": "", "sources": ["../../../src/server/webStandardStreamableHttp.ts"], "names": [], "mappings": "AAAA;;;;;;;GAOG;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AACnD,OAAO,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAC3C,OAAO,EACH,gBAAgB,EAMhB,cAAc,EAEd,SAAS,EAGZ,MAAM,aAAa,CAAC;AAErB,MAAM,MAAM,QAAQ,GAAG,MAAM,CAAC;AAC9B,MAAM,MAAM,OAAO,GAAG,MAAM,CAAC;AAE7B;;GAEG;AACH,MAAM,WAAW,UAAU;IACvB;;;;;OAKG;IACH,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;IAE1E;;;;;;;OAOG;IACH,qBAAqB,CAAC,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC;IAExE,iBAAiB,CACb,WAAW,EAAE,OAAO,EACpB,EACI,IAAI,EACP,EAAE;QACC,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;KACtE,GACF,OAAO,CAAC,QAAQ,CAAC,CAAC;CACxB;AAgBD;;GAEG;AACH,MAAM,WAAW,+CAA+C;IAC5D;;;;;OAKG;IACH,kBAAkB,CAAC,EAAE,MAAM,MAAM,CAAC;IAElC;;;;;;OAMG;IACH,oBAAoB,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEnE;;;;;;;;;OASG;IACH,eAAe,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9D;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAE7B;;;OAGG;IACH,UAAU,CAAC,EAAE,UAAU,CAAC;IAExB;;;;OAIG;IACH,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;IAExB;;;;OAIG;IACH,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC;IAE1B;;;;OAIG;IACH,4BAA4B,CAAC,EAAE,OAAO,CAAC;IAEvC;;;;OAIG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;CAC1B;AAED;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACjC;;;OAGG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;IAErB;;OAEG;IACH,QAAQ,CAAC,EAAE,QAAQ,CAAC;CACvB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0CG;AACH,qBAAa,wCAAyC,YAAW,SAAS;IAEtE,OAAO,CAAC,kBAAkB,CAA6B;IACvD,OAAO,CAAC,QAAQ,CAAkB;IAClC,OAAO,CAAC,cAAc,CAAyC;IAC/D,OAAO,CAAC,uBAAuB,CAAqC;IACpE,OAAO,CAAC,mBAAmB,CAA6C;IACxE,OAAO,CAAC,YAAY,CAAkB;IACtC,OAAO,CAAC,mBAAmB,CAAkB;IAC7C,OAAO,CAAC,sBAAsB,CAAyB;IACvD,OAAO,CAAC,WAAW,CAAC,CAAa;IACjC,OAAO,CAAC,qBAAqB,CAAC,CAA8C;IAC5E,OAAO,CAAC,gBAAgB,CAAC,CAA8C;IACvE,OAAO,CAAC,aAAa,CAAC,CAAW;IACjC,OAAO,CAAC,eAAe,CAAC,CAAW;IACnC,OAAO,CAAC,6BAA6B,CAAU;IAC/C,OAAO,CAAC,cAAc,CAAC,CAAS;IAEhC,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,OAAO,CAAC,EAAE,MAAM,IAAI,CAAC;IACrB,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IACjC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,KAAK,CAAC,EAAE,gBAAgB,KAAK,IAAI,CAAC;gBAE5D,OAAO,GAAE,+CAAoD;IAYzE;;;OAGG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAO5B;;OAEG;IACH,OAAO,CAAC,uBAAuB;IA0B/B;;;OAGG;IACH,OAAO,CAAC,sBAAsB;IA6B9B;;;OAGG;IACG,aAAa,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,oBAAoB,GAAG,OAAO,CAAC,QAAQ,CAAC;IAmBpF;;;;OAIG;YACW,iBAAiB;IA0B/B;;OAEG;YACW,gBAAgB;IA2E9B;;;OAGG;YACW,YAAY;IAgF1B;;OAEG;IACH,OAAO,CAAC,aAAa;IAoBrB;;OAEG;IACH,OAAO,CAAC,wBAAwB;IAoBhC;;OAEG;YACW,iBAAiB;IA8M/B;;OAEG;YACW,mBAAmB;IAejC;;;OAGG;IACH,OAAO,CAAC,eAAe;IA0BvB;;;;;;;;;;;;OAYG;IACH,OAAO,CAAC,uBAAuB;IAazB,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAY5B;;;;OAIG;IACH,cAAc,CAAC,SAAS,EAAE,SAAS,GAAG,IAAI;IAU1C;;;OAGG;IACH,wBAAwB,IAAI,IAAI;IAO1B,IAAI,CAAC,OAAO,EAAE,cAAc,EAAE,OAAO,CAAC,EAAE;QAAE,gBAAgB,CAAC,EAAE,SAAS,CAAA;KAAE,GAAG,OAAO,CAAC,IAAI,CAAC;CAiGjG"}