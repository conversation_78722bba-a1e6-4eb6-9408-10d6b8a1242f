import { agent, LLMHandle, llmOpenAI, llmOpenAIResponses, mcp } from "volcano-sdk";
import { CONFIG, MCPServer, MCPResponse } from './config.js';
import { Logger } from './logger.js';
import { parseArgs, getSearchQuery } from './cli.js';

// Validate environment variables
function validateEnvironment(): string {
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    Logger.error('OPENAI_API_KEY environment variable is required');
    process.exit(1);
  }
  Logger.success('Environment validation passed');
  return apiKey;
}

// Create LLM instances
function createLLMInstances(apiKey: string) {
  const llm = llmOpenAI({
    apiKey,
    model: CONFIG.OPENAI_MODEL,
  });

  const structuredLlm = llmOpenAIResponses({
    apiKey,
    model: CONFIG.OPENAI_MODEL,
    options: {
      jsonSchema: {
        name: "mcp_response",
        description: "MCP Server information",
        schema: {
          type: "object",
          properties: {
            data: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  url: { type: "string" },
                  name: { type: "string" },
                  description: { type: "string" },
                },
                required: ["url", "name", "description"],
                additionalProperties: false,
              },
            },
          },
          required: ["data"],
          additionalProperties: false,
        },
      },
    }
  });

  return { llm, structuredLlm };
}

// Search for MCP servers
async function searchMCPServers(structuredLlm: LLMHandle, query: string): Promise<MCPServer[]> {
  Logger.info(`Searching for MCP servers: ${query}`, '🔍');

  const registryMcp = mcp(CONFIG.REGISTRY_URL);

  try {
    const results = await agent({ llm: structuredLlm })
      .then({
        prompt: query,
        mcps: [registryMcp],
      })
      .run();

    // Debug: Log tool calls for the first result
    if (results[0]?.toolCalls) {
      Logger.debug('Tool calls from first result', results[0].toolCalls, '🔧');
    }

    const lastResult = results[results.length - 1];
    if (!lastResult?.llmOutput) {
      throw new Error('No LLM output received from search');
    }

    const response: MCPResponse = JSON.parse(lastResult.llmOutput || CONFIG.DEFAULT_RESPONSE);
    return response.data || [];
  } catch (error) {
    Logger.error(`Error searching MCP servers: ${error}`);
    return [];
  }
}

// Get a joke from Chuck Norris MCP server
async function getChuckNorrisJoke(llm: LLMHandle, serverUrl: string, topic: string): Promise<string> {
  Logger.info(`Getting Chuck Norris joke about: ${topic}`, '😄');

  try {
    const chuckNorrisMcp = mcp(serverUrl);

    const results = await agent({ llm })
      .then({
        prompt: `Tell me a Chuck Norris joke about ${topic}`,
        mcps: [chuckNorrisMcp],
      })
      .run();

    const lastResult = results[results.length - 1];
    return lastResult?.llmOutput || 'No joke received';
  } catch (error) {
    Logger.error(`Error getting Chuck Norris joke: ${error}`);
    return 'Failed to get joke';
  }
}

// Main execution function
async function main(): Promise<void> {
  try {
    // Parse command line arguments
    const options = parseArgs();

    Logger.info(`Starting Volcano Agent for ${options.searchType}`, '🌋');
    Logger.debug('CLI options', options);

    // Validate environment and create LLM instances
    const apiKey = validateEnvironment();
    const { llm, structuredLlm } = createLLMInstances(apiKey);

    // Search for MCP servers based on search type
    const searchQuery = getSearchQuery(options.searchType);
    const mcpServers = await searchMCPServers(structuredLlm, searchQuery);

    Logger.info(`Found ${mcpServers.length} MCP servers`, '📋');
    Logger.debug('MCP servers details', mcpServers);

    if (mcpServers.length === 0) {
      Logger.error(`No MCP servers found for ${options.searchType}`);
      process.exit(1);
    }

    // Use the first server
    const selectedServer = mcpServers[0];
    Logger.success(`Using MCP server: ${selectedServer.name} (${selectedServer.url})`);

    // Handle different search types
    if (options.searchType === 'chuck-norris') {
      // Get a Chuck Norris joke
      const joke = await getChuckNorrisJoke(llm, selectedServer.url, options.topic);

      Logger.joke('\nChuck Norris Joke:');
      console.log(joke);
    } else if (options.searchType === 'github-issues') {
      Logger.info('GitHub issues MCP server found. You can now use it to check your issues.');
      Logger.info(`Server URL: ${selectedServer.url}`);
      Logger.info(`Description: ${selectedServer.description}`);
    }

    Logger.success('Application completed successfully', '🎉');

  } catch (error) {
    Logger.error(`Fatal error: ${error}`, '💥');
    process.exit(1);
  }
}

// Run the application
main().catch((error) => {
  Logger.error(`Unhandled error: ${error}`, '🚨');
  process.exit(1);
});


> ## Documentation Index
> Fetch the complete documentation index at: https://developer.box.com/llms.txt
> Use this file to discover all available pages before exploring further.

# Remote Box MCP server

[Remote Box MCP server](https://modelcontextprotocol.io/introduction) is a standardized way for AI Agents to connect and interact with third-party applications like Box, enabling seamless access to content and AI capabilities across platforms. The Box MCP server acts as a bridge that allows leading AI Agent platforms — such as Copilot Studio, Claude Enterprise, Mistral Le Chat — to securely query and utilize Box data and AI-powered tools without exposing raw file content. Through OAuth authorization, users can grant AI Agents controlled access to their Box accounts, enabling intelligent document handling, advanced search, and multi-file AI queries directly within these external AI environments.

## Access and manage predefined Box MCP servers

1. Click **Integrations** in the sidebar of Box Admin Console.
2. Use *Categories* filter and choose MCP or search for a predefined Box MCP server in the search field search at the top of the window.
3. Beside the selected MCP server, click the state, then select the state you want to enable.

<Frame noborder center>
    <img src="https://mintcdn.com/box/J_EwM_J-GUl8Mc67/guides/box-mcp/remote/img/integrations-mcp.png?fit=max&auto=format&n=J_EwM_J-GUl8Mc67&q=85&s=8478441658451ba71b6957b62244181a" alt="MCP" data-og-width="3024" width="3024" data-og-height="1722" height="1722" data-path="guides/box-mcp/remote/img/integrations-mcp.png" data-optimize="true" data-opv="3" srcset="https://mintcdn.com/box/J_EwM_J-GUl8Mc67/guides/box-mcp/remote/img/integrations-mcp.png?w=280&fit=max&auto=format&n=J_EwM_J-GUl8Mc67&q=85&s=645763381b13ff57d960b1f6c5a46fd1 280w, https://mintcdn.com/box/J_EwM_J-GUl8Mc67/guides/box-mcp/remote/img/integrations-mcp.png?w=560&fit=max&auto=format&n=J_EwM_J-GUl8Mc67&q=85&s=5611475db22edda91a96c9069bc3334c 560w, https://mintcdn.com/box/J_EwM_J-GUl8Mc67/guides/box-mcp/remote/img/integrations-mcp.png?w=840&fit=max&auto=format&n=J_EwM_J-GUl8Mc67&q=85&s=014c39bf05389b054517d4bb6e44deb7 840w, https://mintcdn.com/box/J_EwM_J-GUl8Mc67/guides/box-mcp/remote/img/integrations-mcp.png?w=1100&fit=max&auto=format&n=J_EwM_J-GUl8Mc67&q=85&s=5ff3671e722e39c8fff77e97e02a8e2c 1100w, https://mintcdn.com/box/J_EwM_J-GUl8Mc67/guides/box-mcp/remote/img/integrations-mcp.png?w=1650&fit=max&auto=format&n=J_EwM_J-GUl8Mc67&q=85&s=70d4134f7631a51be227781335e10031 1650w, https://mintcdn.com/box/J_EwM_J-GUl8Mc67/guides/box-mcp/remote/img/integrations-mcp.png?w=2500&fit=max&auto=format&n=J_EwM_J-GUl8Mc67&q=85&s=c1fae89b9a1e3a40d8a5f9403beac60c 2500w" />
</Frame>

## Create an unlisted Box MCP server

1. Click **Integrations** in the sidebar of Box Admin Console.
2. Search for **Box MCP server** in the search field search at the top of the window.
3. Hover on the **Box MCP server** application, then click **Configure**.
4. In the **Additional Configuration** section, click on **+ Add Integration Credentials**.
5. Enter integration name and click **Save**.
6. Expand details of the newly created entry.
7. Copy the generated **Client ID** and **Client Secret**.
8. Enter the **Redirect URI** provided by the external MCP client.
9. Under **Access Scopes**, enable **Content Actions**.

## Add Box MCP server on the Client Side

Exact steps for adding Box MCP server may vary depending on the AI platform. Refer to your platform’s documentation for client-side setup instructions. Check this sample code for reference:

To connect to Box from the AI Agent platform, you need to:

* Add endpoint URL: `https://mcp.box.com`
* Pass client ID and client secret. Box generates these in the Integration Credentials section of your Admin Console when configuring the Box MCP server, above.
* Pass an MCP name: `box-remote-mcp`
* Provide an `authorization_token`

```python  theme={null}
  response = await client.beta.messages.create(
      model="claude-3-opus-20240229",  # Or your preferred model
      max_tokens=4096,
      messages=conversation_history,
      mcp_servers=[
          {
              "type": "url",
              "url": "https://mcp.box.com",
              "name": "box-remote-mcp",
              "authorization_token": BEARER_TOKEN,
          }
      ],
      betas=["mcp-client-2025-04-04"]
  )
```

### Amazon Quick Suite

To add the remote Box MCP server in Amazon Quick Suite, follow these steps:

1. In the Amazon Quick Suite console, choose **Integrations** and create new integration by choosing **Model Content Protocol**.
2. Enter a name and a description in the **Create integration** page.
3. Set the MCP server endpoint to `https://mcp.box.com`.
4. Choose Auto-publishing to make the integration immediately available for personal use.
5. Click **Next**, select the authentication method, and provide the required configuration.

* For OAuth, use the client credentials you created in the Box Developer Console when configuring the Box MCP server.
* Add token URL: `https://api.box.com/oauth2/token`.
* Add authorization URL: `https://account.box.com/api/oauth2/authorize`.
* Ensure the Amazon Quick Suite redirect URI is allow‑listed in Box Platform App settings.

6. Grant access to Box.
7. Select **Create and continue**, review the integration, then **Next**. Optionally share the integration with other users.

For details, see Amazon Quick Suite documentation: [Model Context Protocol (MCP) integration](https://docs.aws.amazon.com/quicksuite/latest/userguide/mcp-integration.html).

<iframe width="100%" height="315" src="https://www.youtube.com/embed/S2kns2tYJjA?si=AWuXpvrjJY0J0qBb" title="Box remote MCP with Amazon Quick Suite" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen />

### Anthropic's Messages API

Connect the remote Box MCP server with [Anthropic's Messages API](https://docs.anthropic.com/en/api/messages). Clone [this sample chat bot project](https://github.com/box-community/mcp-client-remote-mcp-react) to get started quickly. It allows you to have a conversation with an Anthropic model, which has access to tools provided by the Box remote MCP server.

Watch a video that features a demo project, a React based chat bot that leverages remote Box MCP.

<iframe width="100%" height="315" src="https://www.youtube.com/embed/Rcn8p-nLJtI?si=dWsMnE6reTE-aKQw" title="Box remote MCP" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen />

### Azure API Center

To add the remote Box MCP server in Azure API Center's Enterprise Registry, follow the instructions provided by Azure. Detailed steps and guidance are available in the official Microsoft documentation: [Add an MCP Server in Azure API Center's Enterprise Registry](https://learn.microsoft.com/en-us/azure/api-center/register-discover-mcp-server).

### ChatGPT

To add the remote Box MCP server in ChatGPT, follow these steps:

#### Step 1: Configure Box integration

1. Go to **Box** > **Admin Console** > **Integrations** and find **Box MCP Server** either by using the MCP Category filter, or by using the search bar at the top of the page.
2. Go to **Configuration** > **Add Integration Credentials** to generate new credentials.
3. In **Redirect URIs**, change the Box redirect URIs to: [https://chatgpt.com/connector\_platform\_oauth\_redirect](https://chatgpt.com/connector_platform_oauth_redirect).
4. Copy the **Client ID** and **Client Secret** for later use. These are required later for ChatGPT to make the authorization connection.
5. Check the **Access** scopes:
   * Read all files and folders stored in Box.
   * Read and write all files and folders stored in Box.
   * Manage AI Requests.
6. Click **Save**.

#### Step 2: Enable developer mode in ChatGPT

1. In ChatGPT, as a ChatGPT Workspace admin, click the profile in the bottom-left corner.
2. Click **Settings** > **Apps & Connectors** > **Advanced Settings**.
3. Toggle the **Developer mode** on. This enables you to add custom connectors.

#### Step 3: Create the connector

1. Go back to **Apps & Connectors**.
2. Click **Create** at the top-right corner of the modal.
3. Fill in the data:
   * **Name**: For example, `Box MCP Server Connector`.
   * **MCP Server URL**: [https://mcp.box.com](https://mcp.box.com)
   * **OAuth Client ID**: Found in Box **Admin Console** > **Integrations** > **Box MCP Server** > **Configuration**.
   * **OAuth Client Secret**: Found in Box **Admin Console** > **Integrations** > **Box MCP Server** > **Configuration**.
4. Select the `I understand and want to continue` checkbox and click **Create**.
5. Authorize the Box user.

<Note>
  If an error displays, such as `Something went wrong with setting up the connection`, you can continue to click through. The setup should work even if this displays.
</Note>

#### Step 4: Test and deploy

1. In the chat, select the **+** icon, then hover over **More** and select the newly added connector.
2. Make sure that the default OpenAI Box connector is turned off or disconnected, so the answer is provided by the custom connector.
3. If you want to add this connector for the whole workspace, you can publish it within [ChatGPT Workspace](https://help.openai.com/en/articles/12584461-developer-mode-and-full-mcp-connectors-in-chatgpt-beta#:~:text=Publish%20connector,settings%20in%20ChatGPT).

### Claude

To start using the remote Box MCP server in [Claude](https://claude.ai/directory), go to Claude app settings and click *Connectors*. Within this view click *Browse connectors* and choose Box. Ensure to grant Claude access to Box.

See a demo video that showcases how to connect Box MCP with the Claude desktop app.

<iframe width="100%" height="315" src="https://www.youtube.com/embed/OJVjXZkPd54?si=PSiV6aKkUnL7CXoG" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen />

### Copilot Studio

To add the remote Box MCP server, follow the instructions provided by the Copilot Studio side. Detailed steps and guidance are available in the official Microsoft documentation: [Add an MCP Server in Copilot Studio](https://learn.microsoft.com/en-us/microsoft-copilot-studio/agent-extend-action-mcp#add-tools-from-an-existing-mcp-connector-to-an-agent).

### GitHub Copilot

To set up the Box MCP server with GitHub Copilot, go to the [overview page](https://github.com/mcp/box/mcp-server-box-remote) and click *Install MCP server*. You'll be redirected to the VS Code editor. Click *Install* and complete OAuth to grant access to Box.

You may need to manually enter your Client ID and Client Secret. When registering your OAuth application, make sure to include following redirect URIs:

```sh  theme={null}
http://127.0.0.1:33418
https://vscode.dev/redirect
```

<iframe width="100%" height="315" src="https://www.youtube.com/embed/Y1a16jhCVPc?si=MOutZC3_tGtpcJ7c" title="Box remote MCP with Github Copilot" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen />

### Groq

To integrate Box MCP server with Groq, follow the tutorial available in the [Groq API Cookbook](https://github.com/groq/groq-api-cookbook/tree/main/tutorials/03-mcp/mcp-box). This tutorial provides step-by-step instructions for setting up and using Box MCP with Groq.

Watch a [video demonstration](https://groq.enterprise.slack.com/files/U089GJTGFFA/F09QXEZ3V17/groq_box_mcp_tutorial__1.mp4) of the Groq and Box MCP integration.

### LangSmith Agent Builder

The video below shows how to use the Box MCP server with [LangChain Agent Builder](https://docs.langchain.com/langsmith/agent-builder-remote-mcp-servers) to build a reusable intake review agent. The agent securely connects to Box, analyzes files with Box AI, extracts structured data, and saves a review packet back to the source folder.

In this guide, you will:

* Connect LangChain Agent Builder to Box using MCP and OAuth
* Use a prebuilt agent template for intake review workflows
* Run the agent with a simple prompt
* Extract key fields, classify documents, and flag missing information
* Generate a durable review artifact saved back into Box

<iframe width="100%" height="315" src="https://www.youtube.com/embed/KZUcueJ73-Q?si=uDK9isZP8H10ptWc" title="Box remote MCP with LangChain Agent Builder" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen />

### Microsoft Foundry Tools

Box is available in the Microsoft Foundry Tools catalog. To add the remote Box MCP server, refer to the official Microsoft documentation: [Discover tools in the Microsoft Foundry Tools](https://learn.microsoft.com/en-us/azure/ai-foundry/agents/concepts/tool-catalog?view=foundry).

### Mistral AI’s Le Chat

To enable Box MCP for [Mistral AI’s Le Chat](https://chat.mistral.ai/chat), visit [connectors page](https://chat.mistral.ai/connections), click *Connect* and grant access to Box. Alternatively, directly form the chat view, click *Tools* and choose Box.

<Frame noborder center>
    <img src="https://mintcdn.com/box/J_EwM_J-GUl8Mc67/guides/box-mcp/remote/img/mistral.gif?s=********************************" alt="MCP" data-og-width="1152" width="1152" data-og-height="648" height="648" data-path="guides/box-mcp/remote/img/mistral.gif" data-optimize="true" data-opv="3" />
</Frame>

### OpenAI AgentKit

Box enables agentic enterprise support with OpenAI AgentKit. For more information and a demonstration, see the [Box blog post](https://blog.box.com/box-enables-agentic-enterprise-support-openais-new-agentkit).

### Windows

Box MCP server is available for Windows. For more information, visit the [Windows Agentic](https://developer.microsoft.com/en-us/windows/agentic/) and [MCP servers on Windows overview](https://learn.microsoft.com/en-us/windows/ai/mcp/servers/mcp-server-overview) pages.

<Message type="notice">
  This integration is currently in preview and the customer experience may change.
</Message>

### Using Box AI Features with the MCP Server

When you use Box AI in third-party applications, you can achieve the best experience and highest quality results by accessing the applications through the Box MCP server. This ensures full functionality, improved performance, and a seamless user experience.

## Available tools

The remote Box MCP server provides access to a comprehensive set of tools that enable AI agents to interact with Box content and features. These tools are organized by functional category to help you quickly identify the right capabilities for your use case.

### User and authentication

Manage user authentication and retrieve information about the authenticated user.

| Tool       | Description                                                              |
| ---------- | ------------------------------------------------------------------------ |
| `who_am_i` | Returns detailed information about the currently authenticated Box user. |

### Content management

Manage files and folders, perform searches, and handle content operations across your Box environment.

#### File operations

| Tool                     | Description                                                                                         |
| ------------------------ | --------------------------------------------------------------------------------------------------- |
| `get_file_content`       | Returns the content of a file stored in Box.                                                        |
| `get_file_details`       | Gets comprehensive file information from Box including metadata, permissions, and version details.  |
| `update_file_properties` | Updates file properties, including name, description, tags, and collections.                        |
| `upload_file`            | Uploads a new file to Box.                                                                          |
| `upload_file_version`    | Uploads a new file version by providing the entire file contents to update an existing file in Box. |

#### Folder operations

| Tool                               | Description                                                                                             |
| ---------------------------------- | ------------------------------------------------------------------------------------------------------- |
| `create_folder`                    | Creates a new folder in Box.                                                                            |
| `get_folder_details`               | Retrieves comprehensive folder information including metadata, permissions, and collaboration settings. |
| `list_folder_content_by_folder_id` | Lists files, folders, and web links in a folder.                                                        |
| `update_folder_properties`         | Updates folder properties, including name, description, tags, and collections.                          |

#### Search

| Tool                     | Description                                                                                                  |
| ------------------------ | ------------------------------------------------------------------------------------------------------------ |
| `search_files_keyword`   | Searches for files using keywords. Supports metadata filters, file extension filtering, and field selection. |
| `search_folders_by_name` | Searches for folders within Box by name using keyword matching.                                              |

### Box AI

Use AI-powered tools to ask questions, extract insights, and analyze content across files and hubs.

| Tool                    | Description                                                                                                                     |
| ----------------------- | ------------------------------------------------------------------------------------------------------------------------------- |
| `ai_qa_hub`             | Asks a question to a Box Hub using Box AI.                                                                                      |
| `ai_qa_single_file`     | Asks a question to a single file using Box AI.                                                                                  |
| `ai_qa_multi_file`      | Asks a question to multiple files using Box AI.                                                                                 |
| `ai_extract_freeform`   | Extracts metadata from files using Box AI in freeform format without requiring predefined template structures.                  |
| `ai_extract_structured` | Extracts structured metadata from files using Box AI based on either custom fields definition or an existing metadata template. |

### Collaboration

Enable team collaboration through comments, shared links, and collaboration management tools.

| Tool                  | Description                                                                                |
| --------------------- | ------------------------------------------------------------------------------------------ |
| `create_file_comment` | Creates a new comment on a specific file.                                                  |
| `list_file_comments`  | Lists all comments on a specific file.                                                     |
| `list_tasks`          | Lists all tasks associated with a specific file, including status, message, and due dates. |

### Hubs

Create and manage Box Hubs for organizing collaborative content and resources around specific topics or projects.

| Tool               | Description                                                    |
| ------------------ | -------------------------------------------------------------- |
| `add_items_to_hub` | Adds files, folders, or web links to a specific hub.           |
| `create_hub`       | Creates a new hub.                                             |
| `get_hub_details`  | Retrieves detailed information about a specific hub.           |
| `get_hub_items`    | Gets items (files and folders) associated with a specific hub. |
| `list_hubs`        | Lists all hubs accessible to the authenticated user.           |
| `update_hub`       | Updates the title and description of a specific hub.           |

## Feedback

To share feedback or ask questions about Box MCP server, visit the [Box AI API thread](https://community.box.com/box-ai-api-8) of the Box Developer Community forum.
