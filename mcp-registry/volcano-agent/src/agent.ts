import { agent, LLMHandle, llmOpenAI, llmOpenAIResponses, mcp } from "volcano-sdk";

const llm = llmOpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
  model: "gpt-4o-mini",
});

const structuredLlm = llmOpenAIResponses({
  apiKey: process.env.OPENAI_API_KEY!,
  model: "gpt-4o-mini",
  options: {
    jsonSchema: {
      name: "mcp_response",
      description: "MCP Server information",
      schema: {
        type: "object",
        properties: {
          data: {
            type: "array",
            items: {
              type: "object",
              properties: {
                url: { type: "string" },
                name: { type: "string" },
                description: { type: "string" },
              },
              required: ["url", "name", "description"],
              additionalProperties: false,
            },
          },
        },
        required: ["data"],
        additionalProperties: false,
      },
    },
  }
},
);

const registryMcp = mcp("http://localhost:8000/mcp/registry");

const results = await agent({ llm: structuredLlm })
  .then({
    prompt: "Search for a MCP server that handles chuck norris jokes. Include url, name, and description for each server.",
    // prompt: "Return all MCP servers that allow me to check my github issues. Include url, name, and description for each server.",
    mcps: [registryMcp],
  })
  .run();

console.log(results[0].toolCalls); 

const result_mcp_object = JSON.parse(results[results.length - 1]?.llmOutput || '{"data":[]}');
const result_mcp_array = result_mcp_object.data || [];

console.log('Found MCP servers:', result_mcp_array);

// Use the first server from the array
const firstMcpServer = result_mcp_array[0];
if (!firstMcpServer) {
  console.error('No MCP servers found in the response');
  process.exit(1);
}

console.log('Using MCP server:', firstMcpServer.url);

const chuckNorrisMcp = mcp(firstMcpServer.url);

const chuckNorrisResults = await agent({ llm })
  .then({
    prompt: "Tell me a Chuck Norris joke about history",
    mcps: [chuckNorrisMcp],
  })
  .run();

// console.log('\n' + JSON.stringify(results[results.length - 1]?.toolCalls || ''));
console.log('\n' + (chuckNorrisResults[chuckNorrisResults.length - 1]?.llmOutput || ''));

// Use conversational results API for clean output
// const summary = await results.summary(llm);
// console.log("\n" + summary);

process.exit(0);