export const CONFIG = {
  // OpenAI Configuration
  OPENAI_MODEL: "gpt-4o-mini",
  
  // MCP Configuration
  REGISTRY_URL: "http://localhost:8000/mcp/registry",
  
  // Response defaults
  DEFAULT_RESPONSE: '{"data":[]}',
  
  // Prompts
  PROMPTS: {
    CHUCK_NORRIS_SEARCH: "Search for a MCP server that handles chuck norris jokes. Include url, name, and description for each server.",
    GITHUB_ISSUES_SEARCH: "Return all MCP servers that allow me to check my github issues. Include url, name, and description for each server.",
  },
  
  // Logging
  ENABLE_DEBUG: process.env.DEBUG === 'true',
} as const;

export interface MCPServer {
  url: string;
  name: string;
  description: string;
}

export interface MCPResponse {
  data: MCPServer[];
}
