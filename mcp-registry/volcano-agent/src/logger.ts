import { CONFIG } from './config.js';

export class Logger {
  private static formatMessage(level: string, message: string, emoji: string = ''): string {
    const timestamp = new Date().toISOString();
    return `[${timestamp}] ${emoji} ${level.toUpperCase()}: ${message}`;
  }

  static info(message: string, emoji: string = 'ℹ️'): void {
    console.log(this.formatMessage('info', message, emoji));
  }

  static success(message: string, emoji: string = '✅'): void {
    console.log(this.formatMessage('success', message, emoji));
  }

  static warning(message: string, emoji: string = '⚠️'): void {
    console.warn(this.formatMessage('warning', message, emoji));
  }

  static error(message: string, emoji: string = '❌'): void {
    console.error(this.formatMessage('error', message, emoji));
  }

  static debug(message: string, data?: any, emoji: string = '🔍'): void {
    if (CONFIG.ENABLE_DEBUG) {
      console.log(this.formatMessage('debug', message, emoji));
      if (data !== undefined) {
        console.log(JSON.stringify(data, null, 2));
      }
    }
  }

  static joke(message: string, emoji: string = '🎭'): void {
    console.log(this.formatMessage('joke', message, emoji));
  }
}
