import { agent, llmOpenAI } from "volcano-sdk";
const llm = llmOpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    model: "gpt-4o-mini",
});
const results = await agent({ llm })
    .then({ prompt: "What is the capital of India" })
    .then({ prompt: "Tell me more about it" })
    .run();
// Use conversational results API for clean output
const summary = await results.summary(llm);
console.log("\n" + summary);
process.exit(0);
