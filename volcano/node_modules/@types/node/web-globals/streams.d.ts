export {};

import * as webstreams from "stream/web";

type _ByteLengthQueuingStrategy = typeof globalThis extends { onmessage: any } ? {}
    : webstreams.ByteLengthQueuingStrategy;
type _CompressionStream = typeof globalThis extends { onmessage: any } ? {} : webstreams.CompressionStream;
type _CountQueuingStrategy = typeof globalThis extends { onmessage: any } ? {} : webstreams.CountQueuingStrategy;
type _DecompressionStream = typeof globalThis extends { onmessage: any } ? {} : webstreams.DecompressionStream;
type _QueuingStrategy<T = any> = typeof globalThis extends { onmessage: any } ? {} : webstreams.QueuingStrategy<T>;
type _ReadableByteStreamController = typeof globalThis extends { onmessage: any } ? {}
    : webstreams.ReadableByteStreamController;
type _ReadableStream<R = any> = typeof globalThis extends { onmessage: any } ? {} : webstreams.ReadableStream<R>;
type _ReadableStreamBYOBReader = typeof globalThis extends { onmessage: any } ? {}
    : webstreams.ReadableStreamBYOBReader;
type _ReadableStreamBYOBRequest = typeof globalThis extends { onmessage: any } ? {}
    : webstreams.ReadableStreamBYOBRequest;
type _ReadableStreamDefaultController<R = any> = typeof globalThis extends { onmessage: any } ? {}
    : webstreams.ReadableStreamDefaultController<R>;
type _ReadableStreamDefaultReader<R = any> = typeof globalThis extends { onmessage: any } ? {}
    : webstreams.ReadableStreamDefaultReader<R>;
type _TextDecoderStream = typeof globalThis extends { onmessage: any } ? {} : webstreams.TextDecoderStream;
type _TextEncoderStream = typeof globalThis extends { onmessage: any } ? {} : webstreams.TextEncoderStream;
type _TransformStream<I = any, O = any> = typeof globalThis extends { onmessage: any } ? {}
    : webstreams.TransformStream<I, O>;
type _TransformStreamDefaultController<O = any> = typeof globalThis extends { onmessage: any } ? {}
    : webstreams.TransformStreamDefaultController<O>;
type _WritableStream<W = any> = typeof globalThis extends { onmessage: any } ? {} : webstreams.WritableStream<W>;
type _WritableStreamDefaultController = typeof globalThis extends { onmessage: any } ? {}
    : webstreams.WritableStreamDefaultController;
type _WritableStreamDefaultWriter<W = any> = typeof globalThis extends { onmessage: any } ? {}
    : webstreams.WritableStreamDefaultWriter<W>;

declare global {
    interface ByteLengthQueuingStrategy extends _ByteLengthQueuingStrategy {}
    var ByteLengthQueuingStrategy: typeof globalThis extends { onmessage: any; ByteLengthQueuingStrategy: infer T } ? T
        : typeof webstreams.ByteLengthQueuingStrategy;

    interface CompressionStream extends _CompressionStream {}
    var CompressionStream: typeof globalThis extends {
        onmessage: any;
        CompressionStream: infer T;
    } ? T
        : typeof webstreams.CompressionStream;

    interface CountQueuingStrategy extends _CountQueuingStrategy {}
    var CountQueuingStrategy: typeof globalThis extends { onmessage: any; CountQueuingStrategy: infer T } ? T
        : typeof webstreams.CountQueuingStrategy;

    interface DecompressionStream extends _DecompressionStream {}
    var DecompressionStream: typeof globalThis extends {
        onmessage: any;
        DecompressionStream: infer T;
    } ? T
        : typeof webstreams.DecompressionStream;

    interface QueuingStrategy<T = any> extends _QueuingStrategy<T> {}

    interface ReadableByteStreamController extends _ReadableByteStreamController {}
    var ReadableByteStreamController: typeof globalThis extends
        { onmessage: any; ReadableByteStreamController: infer T } ? T : typeof webstreams.ReadableByteStreamController;

    interface ReadableStream<R = any> extends _ReadableStream<R> {}
    var ReadableStream: typeof globalThis extends { onmessage: any; ReadableStream: infer T } ? T
        : typeof webstreams.ReadableStream;

    interface ReadableStreamBYOBReader extends _ReadableStreamBYOBReader {}
    var ReadableStreamBYOBReader: typeof globalThis extends { onmessage: any; ReadableStreamBYOBReader: infer T } ? T
        : typeof webstreams.ReadableStreamBYOBReader;

    interface ReadableStreamBYOBRequest extends _ReadableStreamBYOBRequest {}
    var ReadableStreamBYOBRequest: typeof globalThis extends { onmessage: any; ReadableStreamBYOBRequest: infer T } ? T
        : typeof webstreams.ReadableStreamBYOBRequest;

    interface ReadableStreamDefaultController<R = any> extends _ReadableStreamDefaultController<R> {}
    var ReadableStreamDefaultController: typeof globalThis extends
        { onmessage: any; ReadableStreamDefaultController: infer T } ? T
        : typeof webstreams.ReadableStreamDefaultController;

    interface ReadableStreamDefaultReader<R = any> extends _ReadableStreamDefaultReader<R> {}
    var ReadableStreamDefaultReader: typeof globalThis extends { onmessage: any; ReadableStreamDefaultReader: infer T }
        ? T
        : typeof webstreams.ReadableStreamDefaultReader;

    interface TextDecoderStream extends _TextDecoderStream {}
    var TextDecoderStream: typeof globalThis extends { onmessage: any; TextDecoderStream: infer T } ? T
        : typeof webstreams.TextDecoderStream;

    interface TextEncoderStream extends _TextEncoderStream {}
    var TextEncoderStream: typeof globalThis extends { onmessage: any; TextEncoderStream: infer T } ? T
        : typeof webstreams.TextEncoderStream;

    interface TransformStream<I = any, O = any> extends _TransformStream<I, O> {}
    var TransformStream: typeof globalThis extends { onmessage: any; TransformStream: infer T } ? T
        : typeof webstreams.TransformStream;

    interface TransformStreamDefaultController<O = any> extends _TransformStreamDefaultController<O> {}
    var TransformStreamDefaultController: typeof globalThis extends
        { onmessage: any; TransformStreamDefaultController: infer T } ? T
        : typeof webstreams.TransformStreamDefaultController;

    interface WritableStream<W = any> extends _WritableStream<W> {}
    var WritableStream: typeof globalThis extends { onmessage: any; WritableStream: infer T } ? T
        : typeof webstreams.WritableStream;

    interface WritableStreamDefaultController extends _WritableStreamDefaultController {}
    var WritableStreamDefaultController: typeof globalThis extends
        { onmessage: any; WritableStreamDefaultController: infer T } ? T
        : typeof webstreams.WritableStreamDefaultController;

    interface WritableStreamDefaultWriter<W = any> extends _WritableStreamDefaultWriter<W> {}
    var WritableStreamDefaultWriter: typeof globalThis extends { onmessage: any; WritableStreamDefaultWriter: infer T }
        ? T
        : typeof webstreams.WritableStreamDefaultWriter;
}
